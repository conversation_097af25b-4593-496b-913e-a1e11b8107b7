/**
 * Magic Menu - Shopping Cart Functionality
 * Handles cart operations, localStorage persistence, and cart UI updates
 */

const Cart = {
    // Cart storage key
    STORAGE_KEY: 'magicMenuCart',
    
    // Tax rate (7.5% VAT in Nigeria)
    TAX_RATE: 0.075,
    
    // Delivery fee
    DELIVERY_FEE: 500, // ₦500

    // Initialize cart
    init() {
        this.loadCart();
        this.setupEventListeners();
        this.updateCartDisplay();
    },

    // Set up event listeners
    setupEventListeners() {
        // Add to cart buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.add-to-cart, .add-to-cart *')) {
                const button = e.target.closest('.add-to-cart');
                if (button) {
                    e.preventDefault();
                    this.handleAddToCart(button);
                }
            }
        });

        // Cart quantity controls
        document.addEventListener('click', (e) => {
            if (e.target.matches('.quantity-increase')) {
                e.preventDefault();
                const itemId = e.target.dataset.itemId;
                this.increaseQuantity(itemId);
            }
            
            if (e.target.matches('.quantity-decrease')) {
                e.preventDefault();
                const itemId = e.target.dataset.itemId;
                this.decreaseQuantity(itemId);
            }
        });

        // Remove item buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.remove-item')) {
                e.preventDefault();
                const itemId = e.target.dataset.itemId;
                this.removeItem(itemId);
            }
        });

        // Clear cart button
        document.addEventListener('click', (e) => {
            if (e.target.matches('.clear-cart')) {
                e.preventDefault();
                this.clearCart();
            }
        });

        // Quantity input changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('.quantity-input')) {
                const itemId = e.target.dataset.itemId;
                const quantity = parseInt(e.target.value) || 1;
                this.updateQuantity(itemId, quantity);
            }
        });
    },

    // Handle add to cart button click
    handleAddToCart(button) {
        const itemData = {
            id: button.dataset.itemId,
            name: button.dataset.itemName,
            price: parseFloat(button.dataset.itemPrice),
            image: button.dataset.itemImage || '',
            category: button.dataset.itemCategory || ''
        };

        const quantity = 1;
        this.addItem(itemData, quantity);
        
        // Show success message
        MagicMenu.showToast(`${itemData.name} added to cart!`, 'success');
        
        // Update button state temporarily
        const originalText = button.textContent;
        button.textContent = 'Added!';
        button.disabled = true;
        
        setTimeout(() => {
            button.textContent = originalText;
            button.disabled = false;
        }, 1000);
    },

    // Add item to cart
    addItem(item, quantity = 1) {
        const existingItem = this.items.find(cartItem => cartItem.id === item.id);
        
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            this.items.push({
                ...item,
                quantity: quantity,
                addedAt: new Date().toISOString()
            });
        }
        
        this.saveCart();
        this.updateCartDisplay();
    },

    // Remove item from cart
    removeItem(itemId) {
        const itemIndex = this.items.findIndex(item => item.id === itemId);
        if (itemIndex > -1) {
            const item = this.items[itemIndex];
            this.items.splice(itemIndex, 1);
            this.saveCart();
            this.updateCartDisplay();
            MagicMenu.showToast(`${item.name} removed from cart`, 'info');
        }
    },

    // Update item quantity
    updateQuantity(itemId, quantity) {
        const item = this.items.find(cartItem => cartItem.id === itemId);
        if (item) {
            if (quantity <= 0) {
                this.removeItem(itemId);
            } else {
                item.quantity = quantity;
                this.saveCart();
                this.updateCartDisplay();
            }
        }
    },

    // Increase item quantity
    increaseQuantity(itemId) {
        const item = this.items.find(cartItem => cartItem.id === itemId);
        if (item) {
            item.quantity += 1;
            this.saveCart();
            this.updateCartDisplay();
        }
    },

    // Decrease item quantity
    decreaseQuantity(itemId) {
        const item = this.items.find(cartItem => cartItem.id === itemId);
        if (item) {
            if (item.quantity > 1) {
                item.quantity -= 1;
                this.saveCart();
                this.updateCartDisplay();
            } else {
                this.removeItem(itemId);
            }
        }
    },

    // Clear entire cart
    clearCart() {
        if (this.items.length > 0) {
            if (confirm('Are you sure you want to clear your cart?')) {
                this.items = [];
                this.saveCart();
                this.updateCartDisplay();
                MagicMenu.showToast('Cart cleared', 'info');
            }
        }
    },

    // Get cart totals
    getTotals() {
        const subtotal = this.items.reduce((total, item) => {
            return total + (item.price * item.quantity);
        }, 0);
        
        const tax = subtotal * this.TAX_RATE;
        const deliveryFee = subtotal > 0 ? this.DELIVERY_FEE : 0;
        const total = subtotal + tax + deliveryFee;
        
        return {
            subtotal: subtotal,
            tax: tax,
            deliveryFee: deliveryFee,
            total: total,
            itemCount: this.items.reduce((count, item) => count + item.quantity, 0)
        };
    },

    // Update cart display throughout the site
    updateCartDisplay() {
        const totals = this.getTotals();
        
        // Update cart count badges
        const cartCountElements = document.querySelectorAll('.cart-count');
        cartCountElements.forEach(element => {
            element.textContent = totals.itemCount;
            element.style.display = totals.itemCount > 0 ? 'inline' : 'none';
        });

        // Update cart total displays
        const cartTotalElements = document.querySelectorAll('.cart-total');
        cartTotalElements.forEach(element => {
            element.textContent = this.formatCurrency(totals.total);
        });

        // Update cart page if we're on it
        this.updateCartPage();
    },

    // Update cart page content
    updateCartPage() {
        const cartContainer = document.querySelector('.cart-items');
        const cartSummary = document.querySelector('.cart-summary');
        
        if (!cartContainer) return;

        if (this.items.length === 0) {
            cartContainer.innerHTML = `
                <div class="empty-cart text-center">
                    <h3>Your cart is empty</h3>
                    <p>Add some delicious Nigerian dishes to get started!</p>
                    <a href="menu.html" class="btn btn-primary">Browse Menu</a>
                </div>
            `;
            if (cartSummary) cartSummary.style.display = 'none';
            return;
        }

        // Render cart items
        cartContainer.innerHTML = this.items.map(item => `
            <div class="cart-item" data-item-id="${item.id}">
                <div class="cart-item-image">
                    <img src="${item.image || 'images/placeholder.jpg'}" alt="${item.name}" loading="lazy">
                </div>
                <div class="cart-item-details">
                    <h4 class="cart-item-name">${item.name}</h4>
                    <p class="cart-item-category">${item.category}</p>
                    <p class="cart-item-price">${this.formatCurrency(item.price)}</p>
                </div>
                <div class="cart-item-quantity">
                    <button type="button" class="quantity-decrease" data-item-id="${item.id}" aria-label="Decrease quantity">-</button>
                    <input type="number" class="quantity-input" data-item-id="${item.id}" value="${item.quantity}" min="1" max="99">
                    <button type="button" class="quantity-increase" data-item-id="${item.id}" aria-label="Increase quantity">+</button>
                </div>
                <div class="cart-item-total">
                    ${this.formatCurrency(item.price * item.quantity)}
                </div>
                <div class="cart-item-actions">
                    <button type="button" class="remove-item btn btn-sm btn-secondary" data-item-id="${item.id}">Remove</button>
                </div>
            </div>
        `).join('');

        // Update cart summary
        if (cartSummary) {
            const totals = this.getTotals();
            cartSummary.style.display = 'block';
            cartSummary.innerHTML = `
                <h3>Order Summary</h3>
                <div class="summary-line">
                    <span>Subtotal:</span>
                    <span>${this.formatCurrency(totals.subtotal)}</span>
                </div>
                <div class="summary-line">
                    <span>VAT (7.5%):</span>
                    <span>${this.formatCurrency(totals.tax)}</span>
                </div>
                <div class="summary-line">
                    <span>Delivery Fee:</span>
                    <span>${this.formatCurrency(totals.deliveryFee)}</span>
                </div>
                <div class="summary-line total">
                    <span><strong>Total:</strong></span>
                    <span><strong>${this.formatCurrency(totals.total)}</strong></span>
                </div>
                <div class="cart-actions">
                    <button type="button" class="clear-cart btn btn-secondary btn-block">Clear Cart</button>
                    <a href="checkout.html" class="btn btn-primary btn-block">Proceed to Checkout</a>
                </div>
            `;
        }
    },

    // Format currency (Nigerian Naira)
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-NG', {
            style: 'currency',
            currency: 'NGN',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    },

    // Save cart to localStorage
    saveCart() {
        try {
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.items));
        } catch (error) {
            console.error('Error saving cart to localStorage:', error);
        }
    },

    // Load cart from localStorage
    loadCart() {
        try {
            const savedCart = localStorage.getItem(this.STORAGE_KEY);
            this.items = savedCart ? JSON.parse(savedCart) : [];
        } catch (error) {
            console.error('Error loading cart from localStorage:', error);
            this.items = [];
        }
    },

    // Get cart data for checkout
    getCartData() {
        return {
            items: this.items,
            totals: this.getTotals()
        };
    },

    // Initialize cart items array
    items: []
};

// Initialize cart when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    Cart.init();
});
