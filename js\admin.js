/**
 * Magic Menu - Admin Dashboard Management
 * Handles admin authentication, session management, and dashboard functionality
 */

const AdminManager = {
    // Configuration
    config: {
        sessionTimeout: 30 * 60 * 1000, // 30 minutes
        maxLoginAttempts: 3,
        lockoutDuration: 15 * 60 * 1000, // 15 minutes
        adminCredentials: {
            // In production, this would be handled by a secure backend
            '<EMAIL>': {
                password: 'MagicMenu2024!',
                role: 'admin',
                name: 'Admin User'
            },
            '<EMAIL>': {
                password: 'Manager2024!',
                role: 'manager',
                name: 'Restaurant Manager'
            }
        }
    },

    // Initialize admin functionality
    init() {
        this.checkAuthentication();
        this.setupEventListeners();
        this.startSessionMonitoring();
        console.log('Admin Manager initialized');
    },

    // Check if user is authenticated
    checkAuthentication() {
        const session = this.getSession();
        
        if (session && this.isSessionValid(session)) {
            this.showAdminDashboard(session.user);
            this.setupAdminNavigation();
        } else {
            this.showLoginForm();
        }
    },

    // Show login form
    showLoginForm() {
        const body = document.body;
        
        // Create login overlay
        const loginOverlay = document.createElement('div');
        loginOverlay.id = 'admin-login-overlay';
        loginOverlay.className = 'admin-login-overlay';
        
        loginOverlay.innerHTML = `
            <div class="admin-login-container">
                <div class="admin-login-form">
                    <h2>Admin Login</h2>
                    <p>Please enter your credentials to access the admin dashboard</p>
                    
                    <form id="admin-login-form">
                        <div class="form-group">
                            <label for="admin-email">Email Address</label>
                            <input type="email" id="admin-email" name="email" required 
                                   class="form-control" placeholder="Enter your email">
                            <div class="error-message" id="admin-email-error"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="admin-password">Password</label>
                            <div class="password-input-container">
                                <input type="password" id="admin-password" name="password" required 
                                       class="form-control" placeholder="Enter your password">
                                <button type="button" class="password-toggle" aria-label="Show password">
                                    <span class="password-toggle-icon">Show</span>
                                </button>
                            </div>
                            <div class="error-message" id="admin-password-error"></div>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="remember" id="admin-remember">
                                <span class="checkmark"></span>
                                Keep me signed in
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block">Sign In</button>
                        
                        <div class="login-attempts" id="login-attempts" style="display: none;">
                            <p class="text-warning">Login attempts remaining: <span id="attempts-count">3</span></p>
                        </div>
                        
                        <div class="lockout-message" id="lockout-message" style="display: none;">
                            <p class="text-error">Account temporarily locked. Please try again in <span id="lockout-timer">15</span> minutes.</p>
                        </div>
                    </form>
                    
                    <div class="admin-login-footer">
                        <p><a href="index.html">← Back to Website</a></p>
                        <p class="demo-credentials">
                            <strong>Demo Credentials:</strong><br>
                            Email: <EMAIL><br>
                            Password: MagicMenu2024!
                        </p>
                    </div>
                </div>
            </div>
        `;
        
        body.appendChild(loginOverlay);
        this.setupLoginForm();
    },

    // Setup login form functionality
    setupLoginForm() {
        const form = document.getElementById('admin-login-form');
        const passwordToggle = document.querySelector('.password-toggle');
        
        // Password toggle functionality
        if (passwordToggle) {
            passwordToggle.addEventListener('click', () => {
                const passwordInput = document.getElementById('admin-password');
                const icon = passwordToggle.querySelector('.password-toggle-icon');
                
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.textContent = 'Hide';
                    passwordToggle.setAttribute('aria-label', 'Hide password');
                } else {
                    passwordInput.type = 'password';
                    icon.textContent = 'Show';
                    passwordToggle.setAttribute('aria-label', 'Show password');
                }
            });
        }
        
        // Form submission
        if (form) {
            form.addEventListener('submit', (e) => this.handleLogin(e));
        }
        
        // Check for existing lockout
        this.checkLockoutStatus();
    },

    // Handle login form submission
    async handleLogin(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        // Clear previous errors
        this.clearLoginErrors();
        
        // Check if account is locked
        if (this.isAccountLocked()) {
            this.showLockoutMessage();
            return;
        }
        
        // Validate form
        if (!this.validateLoginForm(data)) {
            return;
        }
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Signing In...';
        submitBtn.disabled = true;
        
        try {
            // Simulate API delay
            await this.simulateAPICall(1500);
            
            // Authenticate user
            const authResult = this.authenticateUser(data.email, data.password);
            
            if (authResult.success) {
                // Create session
                const session = this.createSession(authResult.user, data.remember === 'on');
                
                // Clear login attempts
                this.clearLoginAttempts();
                
                // Show success message
                if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                    MagicMenu.showToast(`Welcome back, ${authResult.user.name}!`, 'success');
                }
                
                // Remove login overlay and show dashboard
                this.removeLoginOverlay();
                this.showAdminDashboard(authResult.user);
                this.setupAdminNavigation();
                
            } else {
                // Handle failed login
                this.handleFailedLogin();
                this.showLoginError('Invalid email or password. Please try again.');
            }
            
        } catch (error) {
            console.error('Login error:', error);
            this.showLoginError('Login failed. Please try again.');
        } finally {
            // Reset button state
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    },

    // Authenticate user credentials
    authenticateUser(email, password) {
        const user = this.config.adminCredentials[email];
        
        if (user && user.password === password) {
            return {
                success: true,
                user: {
                    email: email,
                    name: user.name,
                    role: user.role,
                    loginTime: new Date().toISOString()
                }
            };
        }
        
        return { success: false };
    },

    // Create admin session
    createSession(user, remember = false) {
        const session = {
            user: user,
            token: this.generateSessionToken(),
            createdAt: Date.now(),
            expiresAt: Date.now() + this.config.sessionTimeout,
            remember: remember
        };
        
        // Store session
        const storage = remember ? localStorage : sessionStorage;
        storage.setItem('adminSession', JSON.stringify(session));
        
        return session;
    },

    // Generate session token
    generateSessionToken() {
        return 'admin_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    },

    // Get current session
    getSession() {
        let session = null;
        
        // Check sessionStorage first, then localStorage
        try {
            session = JSON.parse(sessionStorage.getItem('adminSession')) ||
                     JSON.parse(localStorage.getItem('adminSession'));
        } catch (error) {
            console.error('Error parsing session:', error);
        }
        
        return session;
    },

    // Check if session is valid
    isSessionValid(session) {
        if (!session || !session.token || !session.expiresAt) {
            return false;
        }
        
        // Check if session has expired
        if (Date.now() > session.expiresAt) {
            this.clearSession();
            return false;
        }
        
        return true;
    },

    // Clear session
    clearSession() {
        sessionStorage.removeItem('adminSession');
        localStorage.removeItem('adminSession');
    },

    // Start session monitoring
    startSessionMonitoring() {
        // Check session every minute
        setInterval(() => {
            const session = this.getSession();
            
            if (session && !this.isSessionValid(session)) {
                if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                    MagicMenu.showToast('Session expired. Please log in again.', 'warning');
                }
                this.logout();
            }
        }, 60000); // Check every minute
        
        // Extend session on user activity
        document.addEventListener('click', () => this.extendSession());
        document.addEventListener('keypress', () => this.extendSession());
    },

    // Extend session
    extendSession() {
        const session = this.getSession();
        
        if (session && this.isSessionValid(session)) {
            session.expiresAt = Date.now() + this.config.sessionTimeout;
            
            const storage = session.remember ? localStorage : sessionStorage;
            storage.setItem('adminSession', JSON.stringify(session));
        }
    },

    // Logout
    logout() {
        this.clearSession();
        
        if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
            MagicMenu.showToast('You have been signed out successfully.', 'info');
        }
        
        // Redirect to login
        window.location.reload();
    },

    // Show admin dashboard
    showAdminDashboard(user) {
        // Update header with user info
        const header = document.querySelector('.admin-header');
        if (header) {
            const container = header.querySelector('.container');
            container.innerHTML = `
                <div class="admin-header-content">
                    <div class="admin-title">
                        <h1>Magic Menu Admin Dashboard</h1>
                        <p>Restaurant Management System</p>
                    </div>
                    <div class="admin-user-info">
                        <span class="admin-user-name">Welcome, ${user.name}</span>
                        <span class="admin-user-role">${user.role}</span>
                        <button class="btn btn-secondary btn-sm" onclick="AdminManager.logout()">Logout</button>
                    </div>
                </div>
            `;
        }
    },

    // Remove login overlay
    removeLoginOverlay() {
        const overlay = document.getElementById('admin-login-overlay');
        if (overlay) {
            overlay.remove();
        }
    },

    // Setup admin navigation
    setupAdminNavigation() {
        const navLinks = document.querySelectorAll('.admin-nav-link');
        const sections = document.querySelectorAll('.admin-section');

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();

                const sectionId = link.dataset.section;
                if (!sectionId) return;

                // Update active nav link
                navLinks.forEach(l => l.classList.remove('active'));
                link.classList.add('active');

                // Show corresponding section
                sections.forEach(section => {
                    section.classList.remove('active');
                });

                const targetSection = document.getElementById(`${sectionId}-section`);
                if (targetSection) {
                    targetSection.classList.add('active');
                }
            });
        });
    },

    // Handle failed login attempts
    handleFailedLogin() {
        const attempts = this.getLoginAttempts();
        const newAttempts = attempts + 1;

        localStorage.setItem('adminLoginAttempts', newAttempts.toString());
        localStorage.setItem('adminLastAttempt', Date.now().toString());

        if (newAttempts >= this.config.maxLoginAttempts) {
            this.lockAccount();
        } else {
            this.showAttemptsWarning(this.config.maxLoginAttempts - newAttempts);
        }
    },

    // Get login attempts count
    getLoginAttempts() {
        return parseInt(localStorage.getItem('adminLoginAttempts') || '0');
    },

    // Clear login attempts
    clearLoginAttempts() {
        localStorage.removeItem('adminLoginAttempts');
        localStorage.removeItem('adminLastAttempt');
        localStorage.removeItem('adminAccountLocked');
    },

    // Lock account
    lockAccount() {
        localStorage.setItem('adminAccountLocked', Date.now().toString());
        this.showLockoutMessage();
    },

    // Check if account is locked
    isAccountLocked() {
        const lockedTime = localStorage.getItem('adminAccountLocked');
        if (!lockedTime) return false;

        const lockoutExpiry = parseInt(lockedTime) + this.config.lockoutDuration;
        return Date.now() < lockoutExpiry;
    },

    // Check lockout status
    checkLockoutStatus() {
        if (this.isAccountLocked()) {
            this.showLockoutMessage();
        } else {
            const attempts = this.getLoginAttempts();
            if (attempts > 0) {
                this.showAttemptsWarning(this.config.maxLoginAttempts - attempts);
            }
        }
    },

    // Show attempts warning
    showAttemptsWarning(remaining) {
        const attemptsDiv = document.getElementById('login-attempts');
        const countSpan = document.getElementById('attempts-count');

        if (attemptsDiv && countSpan) {
            countSpan.textContent = remaining;
            attemptsDiv.style.display = 'block';
        }
    },

    // Show lockout message
    showLockoutMessage() {
        const lockoutDiv = document.getElementById('lockout-message');
        const timerSpan = document.getElementById('lockout-timer');
        const form = document.getElementById('admin-login-form');

        if (lockoutDiv && timerSpan && form) {
            const lockedTime = localStorage.getItem('adminAccountLocked');
            const lockoutExpiry = parseInt(lockedTime) + this.config.lockoutDuration;
            const remainingTime = Math.ceil((lockoutExpiry - Date.now()) / 60000);

            timerSpan.textContent = remainingTime;
            lockoutDiv.style.display = 'block';
            form.style.display = 'none';

            // Update timer every minute
            const timer = setInterval(() => {
                const newRemainingTime = Math.ceil((lockoutExpiry - Date.now()) / 60000);
                if (newRemainingTime <= 0) {
                    clearInterval(timer);
                    this.clearLoginAttempts();
                    window.location.reload();
                } else {
                    timerSpan.textContent = newRemainingTime;
                }
            }, 60000);
        }
    },

    // Validate login form
    validateLoginForm(data) {
        let isValid = true;

        if (!data.email) {
            this.showFieldError('admin-email', 'Email address is required.');
            isValid = false;
        } else if (!Utils.isValidEmail(data.email)) {
            this.showFieldError('admin-email', 'Please enter a valid email address.');
            isValid = false;
        }

        if (!data.password) {
            this.showFieldError('admin-password', 'Password is required.');
            isValid = false;
        }

        return isValid;
    },

    // Show field error
    showFieldError(fieldId, message) {
        const errorDiv = document.getElementById(`${fieldId}-error`);
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
    },

    // Show login error
    showLoginError(message) {
        if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
            MagicMenu.showToast(message, 'error');
        } else {
            alert(message);
        }
    },

    // Clear login errors
    clearLoginErrors() {
        const errorDivs = document.querySelectorAll('.error-message');
        errorDivs.forEach(div => {
            div.textContent = '';
            div.style.display = 'none';
        });
    },

    // Setup event listeners
    setupEventListeners() {
        // Listen for storage changes (multi-tab logout)
        window.addEventListener('storage', (e) => {
            if (e.key === 'adminSession' && !e.newValue) {
                // Session was cleared in another tab
                window.location.reload();
            }
        });

        // Handle page visibility change
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                // Page became visible, check session
                const session = this.getSession();
                if (!session || !this.isSessionValid(session)) {
                    this.logout();
                }
            }
        });
    },

    // Simulate API call
    async simulateAPICall(delay = 1000) {
        return new Promise(resolve => setTimeout(resolve, delay));
    }
};
