/**
 * Magic Menu - Admin Menu Management
 * Handles CRUD operations for menu items and categories
 */

const AdminMenuManager = {
    // Initialize menu management
    init() {
        this.loadMenuData();
        this.setupEventListeners();
        this.renderMenuTable();
        this.updateDashboardStats();
        console.log('Admin Menu Manager initialized');
    },

    // Load menu data from the existing structure
    loadMenuData() {
        try {
            // Use the existing menuData from menu-data.js
            if (typeof menuData !== 'undefined') {
                this.menuData = JSON.parse(JSON.stringify(menuData)); // Deep copy
            } else {
                // Fallback to localStorage or default data
                this.menuData = Utils.storage.get('adminMenuData') || this.getDefaultMenuData();
            }
        } catch (error) {
            console.error('Error loading menu data:', error);
            this.menuData = this.getDefaultMenuData();
        }
    },

    // Get default menu data structure
    getDefaultMenuData() {
        return {
            categories: [
                { id: "rice-dishes", name: "Rice Dishes", description: "Flavorful rice dishes" },
                { id: "soups", name: "Traditional Soups", description: "Rich, aromatic soups" },
                { id: "grills", name: "Grilled Specialties", description: "Perfectly seasoned grills" },
                { id: "appetizers", name: "Appetizers & Snacks", description: "Perfect starters" },
                { id: "main-dishes", name: "Main Dishes", description: "Hearty traditional meals" },
                { id: "beverages", name: "Beverages", description: "Refreshing drinks" }
            ],
            items: []
        };
    },

    // Save menu data
    saveMenuData() {
        try {
            // Save to localStorage for persistence
            Utils.storage.set('adminMenuData', this.menuData);
            
            // Update the global menuData if it exists
            if (typeof menuData !== 'undefined') {
                Object.assign(menuData, this.menuData);
            }
            
            return true;
        } catch (error) {
            console.error('Error saving menu data:', error);
            return false;
        }
    },

    // Setup event listeners
    setupEventListeners() {
        // Add new item button
        const addItemBtn = document.querySelector('.section-controls .btn-primary');
        if (addItemBtn && addItemBtn.textContent.includes('Add New Item')) {
            addItemBtn.addEventListener('click', () => this.showAddItemModal());
        }

        // Import/Export buttons
        const importBtn = document.querySelector('.section-controls .btn-secondary');
        if (importBtn && importBtn.textContent.includes('Import')) {
            importBtn.addEventListener('click', () => this.showImportModal());
        }

        const exportBtn = document.querySelectorAll('.section-controls .btn-secondary')[1];
        if (exportBtn && exportBtn.textContent.includes('Export')) {
            exportBtn.addEventListener('click', () => this.exportMenu());
        }

        // Window resize listener for responsive layout
        window.addEventListener('resize', () => {
            clearTimeout(this.resizeTimeout);
            this.resizeTimeout = setTimeout(() => {
                this.renderMenuTable();
            }, 250);
        });
    },

    // Render menu table
    renderMenuTable() {
        const menuSection = document.querySelector('#menu-section');
        if (!menuSection) return;

        // Check if we should use mobile layout
        const isMobile = window.innerWidth <= 767;

        if (isMobile) {
            this.renderMobileMenuCards();
        } else {
            this.renderDesktopMenuTable();
        }
    },

    // Render desktop menu table
    renderDesktopMenuTable() {
        const tableBody = document.querySelector('#menu-section .data-table tbody');
        if (!tableBody) return;

        if (!this.menuData.items || this.menuData.items.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center">
                        <p>No menu items found. <a href="#" onclick="AdminMenuManager.showAddItemModal()">Add your first item</a></p>
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = this.menuData.items.map(item => `
            <tr data-item-id="${item.id}">
                <td>
                    <img src="${item.image || 'images/placeholder.jpg'}"
                         alt="${item.name}"
                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;"
                         onerror="this.src='images/placeholder.jpg'">
                </td>
                <td>
                    <strong>${this.escapeHtml(item.name)}</strong>
                    ${item.description ? `<br><small class="text-muted">${this.escapeHtml(item.description.substring(0, 50))}${item.description.length > 50 ? '...' : ''}</small>` : ''}
                </td>
                <td>${this.getCategoryName(item.category)}</td>
                <td>₦${item.price.toLocaleString()}</td>
                <td>
                    <span class="badge ${item.isAvailable ? 'badge-success' : 'badge-secondary'}">
                        ${item.isAvailable ? 'Available' : 'Unavailable'}
                    </span>
                </td>
                <td>
                    ${item.isPopular ? '<span class="badge badge-primary">Popular</span>' : ''}
                    ${item.spiceLevel ? `<span class="badge badge-info">${item.spiceLevel}</span>` : ''}
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="AdminMenuManager.editItem('${item.id}')">Edit</button>
                        <button class="btn btn-sm ${item.isAvailable ? 'btn-warning' : 'btn-success'}"
                                onclick="AdminMenuManager.toggleAvailability('${item.id}')">
                            ${item.isAvailable ? 'Disable' : 'Enable'}
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="AdminMenuManager.deleteItem('${item.id}')">Delete</button>
                    </div>
                </td>
            </tr>
        `).join('');
    },

    // Render mobile menu cards
    renderMobileMenuCards() {
        const menuSection = document.querySelector('#menu-section');
        const existingTable = menuSection.querySelector('.data-table');

        // Hide desktop table
        if (existingTable) {
            existingTable.style.display = 'none';
        }

        // Create or update mobile container
        let mobileContainer = menuSection.querySelector('.mobile-menu-container');
        if (!mobileContainer) {
            mobileContainer = document.createElement('div');
            mobileContainer.className = 'mobile-menu-container';
            existingTable.parentNode.insertBefore(mobileContainer, existingTable.nextSibling);
        }

        if (!this.menuData.items || this.menuData.items.length === 0) {
            mobileContainer.innerHTML = `
                <div class="mobile-card">
                    <div class="mobile-card-content text-center">
                        <p>No menu items found. <a href="#" onclick="AdminMenuManager.showAddItemModal()">Add your first item</a></p>
                    </div>
                </div>
            `;
            return;
        }

        mobileContainer.innerHTML = this.menuData.items.map(item => `
            <div class="mobile-card" data-item-id="${item.id}">
                <div class="mobile-card-header">
                    <div>
                        <div class="mobile-card-title">${this.escapeHtml(item.name)}</div>
                        <div class="mobile-card-subtitle">${this.getCategoryName(item.category)}</div>
                    </div>
                    <img src="${item.image || 'images/placeholder.jpg'}"
                         alt="${item.name}"
                         style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;"
                         onerror="this.src='images/placeholder.jpg'">
                </div>

                <div class="mobile-card-content">
                    <div class="mobile-card-row">
                        <span class="mobile-card-label">Price:</span>
                        <span class="mobile-card-value">₦${item.price.toLocaleString()}</span>
                    </div>
                    <div class="mobile-card-row">
                        <span class="mobile-card-label">Status:</span>
                        <span class="mobile-card-value">
                            <span class="badge ${item.isAvailable ? 'badge-success' : 'badge-secondary'}">
                                ${item.isAvailable ? 'Available' : 'Unavailable'}
                            </span>
                        </span>
                    </div>
                    ${item.isPopular || item.spiceLevel ? `
                        <div class="mobile-card-row">
                            <span class="mobile-card-label">Tags:</span>
                            <span class="mobile-card-value">
                                ${item.isPopular ? '<span class="badge badge-primary">Popular</span>' : ''}
                                ${item.spiceLevel ? `<span class="badge badge-info">${item.spiceLevel}</span>` : ''}
                            </span>
                        </div>
                    ` : ''}
                    ${item.description ? `
                        <div class="mobile-card-row">
                            <span class="mobile-card-label">Description:</span>
                            <span class="mobile-card-value">${this.escapeHtml(item.description.substring(0, 100))}${item.description.length > 100 ? '...' : ''}</span>
                        </div>
                    ` : ''}
                </div>

                <div class="mobile-card-actions">
                    <button class="btn btn-sm btn-primary" onclick="AdminMenuManager.editItem('${item.id}')">Edit Item</button>
                    <button class="btn btn-sm ${item.isAvailable ? 'btn-warning' : 'btn-success'}"
                            onclick="AdminMenuManager.toggleAvailability('${item.id}')">
                        ${item.isAvailable ? 'Disable' : 'Enable'}
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="AdminMenuManager.deleteItem('${item.id}')">Delete Item</button>
                </div>
            </div>
        `).join('');
    },

    // Get category name by ID
    getCategoryName(categoryId) {
        const category = this.menuData.categories.find(cat => cat.id === categoryId);
        return category ? category.name : 'Unknown Category';
    },

    // Escape HTML to prevent XSS
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    // Show add item modal
    showAddItemModal() {
        this.showItemModal();
    },

    // Edit item
    editItem(itemId) {
        const item = this.menuData.items.find(item => item.id === itemId);
        if (item) {
            this.showItemModal(item);
        }
    },

    // Show item modal (add/edit)
    showItemModal(item = null) {
        const isEdit = !!item;
        const modalTitle = isEdit ? 'Edit Menu Item' : 'Add New Menu Item';
        
        // Create modal HTML
        const modalHtml = `
            <div class="modal-overlay" id="item-modal-overlay">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>${modalTitle}</h3>
                            <button type="button" class="modal-close" onclick="AdminMenuManager.closeItemModal()">&times;</button>
                        </div>
                        <div class="modal-body">
                            <form id="item-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="item-name">Item Name *</label>
                                        <input type="text" id="item-name" name="name" class="form-control" 
                                               value="${item ? this.escapeHtml(item.name) : ''}" required>
                                        <div class="error-message" id="item-name-error"></div>
                                    </div>
                                    <div class="form-group">
                                        <label for="item-category">Category *</label>
                                        <select id="item-category" name="category" class="form-control" required>
                                            <option value="">Select Category</option>
                                            ${this.menuData.categories.map(cat => 
                                                `<option value="${cat.id}" ${item && item.category === cat.id ? 'selected' : ''}>${cat.name}</option>`
                                            ).join('')}
                                        </select>
                                        <div class="error-message" id="item-category-error"></div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="item-description">Description</label>
                                    <textarea id="item-description" name="description" class="form-control" rows="3"
                                              placeholder="Describe the dish...">${item ? this.escapeHtml(item.description || '') : ''}</textarea>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="item-price">Price (₦) *</label>
                                        <input type="number" id="item-price" name="price" class="form-control" 
                                               value="${item ? item.price : ''}" min="0" step="50" required>
                                        <div class="error-message" id="item-price-error"></div>
                                    </div>
                                    <div class="form-group">
                                        <label for="item-image">Image URL</label>
                                        <input type="url" id="item-image" name="image" class="form-control" 
                                               value="${item ? item.image || '' : ''}" placeholder="https://...">
                                    </div>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="item-spice-level">Spice Level</label>
                                        <select id="item-spice-level" name="spiceLevel" class="form-control">
                                            <option value="">None</option>
                                            <option value="mild" ${item && item.spiceLevel === 'mild' ? 'selected' : ''}>Mild</option>
                                            <option value="medium" ${item && item.spiceLevel === 'medium' ? 'selected' : ''}>Medium</option>
                                            <option value="hot" ${item && item.spiceLevel === 'hot' ? 'selected' : ''}>Hot</option>
                                            <option value="very-hot" ${item && item.spiceLevel === 'very-hot' ? 'selected' : ''}>Very Hot</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>Options</label>
                                        <div class="checkbox-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="isAvailable" ${item && item.isAvailable !== false ? 'checked' : ''}>
                                                <span class="checkmark"></span>
                                                Available for order
                                            </label>
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="isPopular" ${item && item.isPopular ? 'checked' : ''}>
                                                <span class="checkmark"></span>
                                                Mark as popular
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="item-allergens">Allergens (comma-separated)</label>
                                    <input type="text" id="item-allergens" name="allergens" class="form-control" 
                                           value="${item && item.allergens ? item.allergens.join(', ') : ''}" 
                                           placeholder="e.g., gluten-free, dairy-free, nuts">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="AdminMenuManager.closeItemModal()">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="AdminMenuManager.saveItem(${isEdit ? `'${item.id}'` : 'null'})">
                                ${isEdit ? 'Update Item' : 'Add Item'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Focus on name field
        setTimeout(() => {
            document.getElementById('item-name').focus();
        }, 100);
    },

    // Close item modal
    closeItemModal() {
        const modal = document.getElementById('item-modal-overlay');
        if (modal) {
            modal.remove();
        }
    },

    // Save item (add or update)
    async saveItem(itemId = null) {
        const form = document.getElementById('item-form');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        // Clear previous errors
        this.clearFormErrors();
        
        // Validate form
        if (!this.validateItemForm(data)) {
            return;
        }
        
        // Show loading state
        const saveBtn = document.querySelector('.modal-footer .btn-primary');
        const originalText = saveBtn.textContent;
        saveBtn.textContent = 'Saving...';
        saveBtn.disabled = true;
        
        try {
            // Simulate API delay
            await this.simulateAPICall(1000);
            
            // Prepare item data
            const itemData = {
                id: itemId || this.generateItemId(),
                name: data.name.trim(),
                description: data.description.trim(),
                price: parseInt(data.price),
                category: data.category,
                image: data.image.trim() || `images/menu/${data.name.toLowerCase().replace(/\s+/g, '-')}.jpg`,
                isAvailable: data.isAvailable === 'on',
                isPopular: data.isPopular === 'on',
                spiceLevel: data.spiceLevel || null,
                allergens: data.allergens ? data.allergens.split(',').map(a => a.trim()).filter(a => a) : []
            };
            
            if (itemId) {
                // Update existing item
                const index = this.menuData.items.findIndex(item => item.id === itemId);
                if (index !== -1) {
                    this.menuData.items[index] = itemData;
                }
            } else {
                // Add new item
                this.menuData.items.push(itemData);
            }
            
            // Save data
            if (this.saveMenuData()) {
                // Show success message
                if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                    MagicMenu.showToast(`Menu item ${itemId ? 'updated' : 'added'} successfully!`, 'success');
                }
                
                // Close modal and refresh table
                this.closeItemModal();
                this.renderMenuTable();
            } else {
                throw new Error('Failed to save menu data');
            }
            
        } catch (error) {
            console.error('Error saving item:', error);
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Failed to save menu item. Please try again.', 'error');
            }
        } finally {
            // Reset button state
            saveBtn.textContent = originalText;
            saveBtn.disabled = false;
        }
    },

    // Generate unique item ID
    generateItemId() {
        return 'item_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },

    // Validate item form
    validateItemForm(data) {
        let isValid = true;
        
        if (!data.name || data.name.trim().length < 2) {
            this.showFieldError('item-name', 'Item name must be at least 2 characters long.');
            isValid = false;
        }
        
        if (!data.category) {
            this.showFieldError('item-category', 'Please select a category.');
            isValid = false;
        }
        
        if (!data.price || parseInt(data.price) < 0) {
            this.showFieldError('item-price', 'Please enter a valid price.');
            isValid = false;
        }
        
        return isValid;
    },

    // Show field error
    showFieldError(fieldId, message) {
        const errorDiv = document.getElementById(`${fieldId}-error`);
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
    },

    // Clear form errors
    clearFormErrors() {
        const errorDivs = document.querySelectorAll('.error-message');
        errorDivs.forEach(div => {
            div.textContent = '';
            div.style.display = 'none';
        });
    },

    // Toggle item availability
    async toggleAvailability(itemId) {
        const item = this.menuData.items.find(item => item.id === itemId);
        if (!item) return;

        try {
            // Show loading state
            const button = document.querySelector(`tr[data-item-id="${itemId}"] .btn-warning, tr[data-item-id="${itemId}"] .btn-success`);
            const originalText = button.textContent;
            button.textContent = 'Updating...';
            button.disabled = true;

            // Simulate API delay
            await this.simulateAPICall(500);

            // Toggle availability
            item.isAvailable = !item.isAvailable;

            // Save data
            if (this.saveMenuData()) {
                // Show success message
                if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                    MagicMenu.showToast(`Item ${item.isAvailable ? 'enabled' : 'disabled'} successfully!`, 'success');
                }

                // Refresh table
                this.renderMenuTable();
            } else {
                throw new Error('Failed to save changes');
            }

        } catch (error) {
            console.error('Error toggling availability:', error);
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Failed to update item. Please try again.', 'error');
            }
        }
    },

    // Delete item with confirmation
    deleteItem(itemId) {
        const item = this.menuData.items.find(item => item.id === itemId);
        if (!item) return;

        // Show confirmation dialog
        this.showConfirmationDialog(
            'Delete Menu Item',
            `Are you sure you want to delete "${item.name}"? This action cannot be undone.`,
            'Delete',
            'btn-danger',
            () => this.confirmDeleteItem(itemId)
        );
    },

    // Confirm delete item
    async confirmDeleteItem(itemId) {
        try {
            // Simulate API delay
            await this.simulateAPICall(500);

            // Remove item from array
            this.menuData.items = this.menuData.items.filter(item => item.id !== itemId);

            // Save data
            if (this.saveMenuData()) {
                // Show success message
                if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                    MagicMenu.showToast('Menu item deleted successfully!', 'success');
                }

                // Refresh table
                this.renderMenuTable();
            } else {
                throw new Error('Failed to save changes');
            }

        } catch (error) {
            console.error('Error deleting item:', error);
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Failed to delete item. Please try again.', 'error');
            }
        }
    },

    // Show confirmation dialog
    showConfirmationDialog(title, message, actionText, actionClass, onConfirm) {
        const modalHtml = `
            <div class="modal-overlay" id="confirmation-modal-overlay">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>${title}</h3>
                            <button type="button" class="modal-close" onclick="AdminMenuManager.closeConfirmationDialog()">&times;</button>
                        </div>
                        <div class="modal-body">
                            <p>${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="AdminMenuManager.closeConfirmationDialog()">Cancel</button>
                            <button type="button" class="btn ${actionClass}" onclick="AdminMenuManager.executeConfirmation()">${actionText}</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Store the confirmation callback
        this.confirmationCallback = onConfirm;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    },

    // Execute confirmation
    executeConfirmation() {
        if (this.confirmationCallback) {
            this.confirmationCallback();
            this.confirmationCallback = null;
        }
        this.closeConfirmationDialog();
    },

    // Close confirmation dialog
    closeConfirmationDialog() {
        const modal = document.getElementById('confirmation-modal-overlay');
        if (modal) {
            modal.remove();
        }
        this.confirmationCallback = null;
    },

    // Export menu data
    exportMenu() {
        try {
            const dataStr = JSON.stringify(this.menuData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `magic-menu-export-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Menu data exported successfully!', 'success');
            }

        } catch (error) {
            console.error('Error exporting menu:', error);
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Failed to export menu data.', 'error');
            }
        }
    },

    // Show import modal
    showImportModal() {
        const modalHtml = `
            <div class="modal-overlay" id="import-modal-overlay">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>Import Menu Data</h3>
                            <button type="button" class="modal-close" onclick="AdminMenuManager.closeImportModal()">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label for="import-file">Select JSON file to import</label>
                                <input type="file" id="import-file" accept=".json" class="form-control">
                                <small class="form-text">Upload a JSON file exported from Magic Menu admin panel.</small>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="import-replace">
                                    <span class="checkmark"></span>
                                    Replace existing menu data (otherwise, merge with current data)
                                </label>
                            </div>

                            <div class="alert alert-warning">
                                <strong>Warning:</strong> Importing menu data will modify your current menu.
                                Consider exporting your current data as a backup first.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="AdminMenuManager.closeImportModal()">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="AdminMenuManager.processImport()">Import</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
    },

    // Close import modal
    closeImportModal() {
        const modal = document.getElementById('import-modal-overlay');
        if (modal) {
            modal.remove();
        }
    },

    // Process import
    async processImport() {
        const fileInput = document.getElementById('import-file');
        const replaceCheckbox = document.getElementById('import-replace');

        if (!fileInput.files.length) {
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Please select a file to import.', 'warning');
            }
            return;
        }

        const file = fileInput.files[0];
        const replace = replaceCheckbox.checked;

        try {
            // Show loading state
            const importBtn = document.querySelector('#import-modal-overlay .btn-primary');
            const originalText = importBtn.textContent;
            importBtn.textContent = 'Importing...';
            importBtn.disabled = true;

            // Read file
            const fileContent = await this.readFile(file);
            const importData = JSON.parse(fileContent);

            // Validate import data
            if (!this.validateImportData(importData)) {
                throw new Error('Invalid menu data format');
            }

            // Simulate API delay
            await this.simulateAPICall(1000);

            if (replace) {
                // Replace all data
                this.menuData = importData;
            } else {
                // Merge data
                this.mergeMenuData(importData);
            }

            // Save data
            if (this.saveMenuData()) {
                // Show success message
                if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                    MagicMenu.showToast('Menu data imported successfully!', 'success');
                }

                // Close modal and refresh table
                this.closeImportModal();
                this.renderMenuTable();
            } else {
                throw new Error('Failed to save imported data');
            }

        } catch (error) {
            console.error('Error importing menu:', error);
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Failed to import menu data. Please check the file format.', 'error');
            }
        }
    },

    // Read file content
    readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsText(file);
        });
    },

    // Validate import data
    validateImportData(data) {
        return data &&
               Array.isArray(data.categories) &&
               Array.isArray(data.items) &&
               data.categories.every(cat => cat.id && cat.name) &&
               data.items.every(item => item.id && item.name && item.price);
    },

    // Merge menu data
    mergeMenuData(importData) {
        // Merge categories (avoid duplicates)
        importData.categories.forEach(importCat => {
            const existingCat = this.menuData.categories.find(cat => cat.id === importCat.id);
            if (!existingCat) {
                this.menuData.categories.push(importCat);
            }
        });

        // Merge items (avoid duplicates, update existing)
        importData.items.forEach(importItem => {
            const existingIndex = this.menuData.items.findIndex(item => item.id === importItem.id);
            if (existingIndex !== -1) {
                // Update existing item
                this.menuData.items[existingIndex] = importItem;
            } else {
                // Add new item
                this.menuData.items.push(importItem);
            }
        });
    },

    // Toggle bulk mode
    toggleBulkMode() {
        const bulkActions = document.getElementById('bulk-actions');
        const tableContainer = document.querySelector('#menu-section');

        if (bulkActions.classList.contains('hidden')) {
            // Enable bulk mode
            bulkActions.classList.remove('hidden');
            tableContainer.classList.add('bulk-mode');
            this.addBulkCheckboxes();
        } else {
            // Disable bulk mode
            this.cancelBulkMode();
        }
    },

    // Cancel bulk mode
    cancelBulkMode() {
        const bulkActions = document.getElementById('bulk-actions');
        const tableContainer = document.querySelector('#menu-section');
        const bulkActionSelect = document.getElementById('bulk-action-select');

        bulkActions.classList.add('hidden');
        tableContainer.classList.remove('bulk-mode');
        bulkActionSelect.value = '';
        this.removeBulkCheckboxes();
    },

    // Add bulk checkboxes to table
    addBulkCheckboxes() {
        const table = document.querySelector('#menu-section .data-table');
        if (!table) return;

        // Add header checkbox
        const headerRow = table.querySelector('thead tr');
        if (headerRow && !headerRow.querySelector('.bulk-checkbox-header')) {
            const headerCheckbox = document.createElement('th');
            headerCheckbox.className = 'bulk-checkbox-header';
            headerCheckbox.innerHTML = `
                <label class="checkbox-label">
                    <input type="checkbox" id="select-all-items" onchange="AdminMenuManager.toggleSelectAll()">
                    <span class="checkmark"></span>
                </label>
            `;
            headerRow.insertBefore(headerCheckbox, headerRow.firstChild);
        }

        // Add row checkboxes
        const bodyRows = table.querySelectorAll('tbody tr');
        bodyRows.forEach(row => {
            if (!row.querySelector('.bulk-checkbox-cell')) {
                const itemId = row.dataset.itemId;
                const checkboxCell = document.createElement('td');
                checkboxCell.className = 'bulk-checkbox-cell';
                checkboxCell.innerHTML = `
                    <label class="checkbox-label">
                        <input type="checkbox" class="item-checkbox" value="${itemId}">
                        <span class="checkmark"></span>
                    </label>
                `;
                row.insertBefore(checkboxCell, row.firstChild);
            }
        });
    },

    // Remove bulk checkboxes
    removeBulkCheckboxes() {
        const headerCheckbox = document.querySelector('.bulk-checkbox-header');
        if (headerCheckbox) {
            headerCheckbox.remove();
        }

        const checkboxCells = document.querySelectorAll('.bulk-checkbox-cell');
        checkboxCells.forEach(cell => cell.remove());
    },

    // Toggle select all
    toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('select-all-items');
        const itemCheckboxes = document.querySelectorAll('.item-checkbox');

        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
    },

    // Execute bulk action
    async executeBulkAction() {
        const actionSelect = document.getElementById('bulk-action-select');
        const selectedAction = actionSelect.value;

        if (!selectedAction) {
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Please select an action.', 'warning');
            }
            return;
        }

        const selectedItems = Array.from(document.querySelectorAll('.item-checkbox:checked'))
            .map(checkbox => checkbox.value);

        if (selectedItems.length === 0) {
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Please select at least one item.', 'warning');
            }
            return;
        }

        // Confirm destructive actions
        if (selectedAction === 'delete') {
            const confirmed = confirm(`Are you sure you want to delete ${selectedItems.length} item(s)? This action cannot be undone.`);
            if (!confirmed) return;
        }

        try {
            // Show loading state
            const applyBtn = document.querySelector('.bulk-actions .btn-warning');
            const originalText = applyBtn.textContent;
            applyBtn.textContent = 'Processing...';
            applyBtn.disabled = true;

            // Simulate API delay
            await this.simulateAPICall(1000);

            let updatedCount = 0;

            // Process each selected item
            selectedItems.forEach(itemId => {
                const itemIndex = this.menuData.items.findIndex(item => item.id === itemId);
                if (itemIndex === -1) return;

                const item = this.menuData.items[itemIndex];

                switch (selectedAction) {
                    case 'enable':
                        item.isAvailable = true;
                        updatedCount++;
                        break;
                    case 'disable':
                        item.isAvailable = false;
                        updatedCount++;
                        break;
                    case 'delete':
                        this.menuData.items.splice(itemIndex, 1);
                        updatedCount++;
                        break;
                    case 'mark-popular':
                        item.isPopular = true;
                        updatedCount++;
                        break;
                    case 'unmark-popular':
                        item.isPopular = false;
                        updatedCount++;
                        break;
                }
            });

            // Save data
            if (this.saveMenuData()) {
                // Show success message
                const actionNames = {
                    'enable': 'enabled',
                    'disable': 'disabled',
                    'delete': 'deleted',
                    'mark-popular': 'marked as popular',
                    'unmark-popular': 'unmarked as popular'
                };

                if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                    MagicMenu.showToast(`${updatedCount} item(s) ${actionNames[selectedAction]} successfully!`, 'success');
                }

                // Cancel bulk mode and refresh table
                this.cancelBulkMode();
                this.renderMenuTable();
            } else {
                throw new Error('Failed to save changes');
            }

        } catch (error) {
            console.error('Error executing bulk action:', error);
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Failed to execute bulk action. Please try again.', 'error');
            }
        } finally {
            // Reset button state
            const applyBtn = document.querySelector('.bulk-actions .btn-warning');
            if (applyBtn) {
                applyBtn.textContent = 'Apply';
                applyBtn.disabled = false;
            }
        }
    },

    // Enhanced dashboard stats
    updateDashboardStats() {
        if (!this.menuData.items) return;

        const totalItems = this.menuData.items.length;
        const availableItems = this.menuData.items.filter(item => item.isAvailable).length;
        const popularItems = this.menuData.items.filter(item => item.isPopular).length;
        const unavailableItems = totalItems - availableItems;

        // Update dashboard if visible
        const dashboardSection = document.getElementById('dashboard-section');
        if (dashboardSection && dashboardSection.classList.contains('active')) {
            // Add quick stats if not exists
            let quickStats = dashboardSection.querySelector('.quick-stats');
            if (!quickStats) {
                quickStats = document.createElement('div');
                quickStats.className = 'quick-stats';
                dashboardSection.querySelector('h2').after(quickStats);
            }

            quickStats.innerHTML = `
                <div class="quick-stat-card">
                    <div class="quick-stat-title">Total Menu Items</div>
                    <div class="quick-stat-value">${totalItems}</div>
                </div>
                <div class="quick-stat-card ${availableItems === totalItems ? 'success' : 'warning'}">
                    <div class="quick-stat-title">Available Items</div>
                    <div class="quick-stat-value">${availableItems}</div>
                </div>
                <div class="quick-stat-card ${unavailableItems === 0 ? 'success' : 'danger'}">
                    <div class="quick-stat-title">Unavailable Items</div>
                    <div class="quick-stat-value">${unavailableItems}</div>
                </div>
                <div class="quick-stat-card">
                    <div class="quick-stat-title">Popular Items</div>
                    <div class="quick-stat-value">${popularItems}</div>
                </div>
            `;
        }
    },

    // Simulate API call
    async simulateAPICall(delay = 1000) {
        return new Promise(resolve => setTimeout(resolve, delay));
    }
};
