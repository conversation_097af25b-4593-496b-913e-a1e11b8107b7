/* Magic Menu - Component Styles */

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-family: var(--font-heading);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    min-height: 44px; /* Touch-friendly size */
    min-width: 44px;
    text-align: center;
    line-height: 1;
}

.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-primary:hover,
.btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: transparent;
    color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover,
.btn-secondary:focus {
    background-color: var(--secondary-color);
    color: var(--white);
    text-decoration: none;
}

.btn-success {
    background-color: var(--success-color);
    color: var(--white);
    border-color: var(--success-color);
}

.btn-success:hover,
.btn-success:focus {
    background-color: #218838;
    border-color: #1e7e34;
    color: var(--white);
    text-decoration: none;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
    min-height: 36px;
}

.btn-block {
    width: 100%;
    display: flex;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Verification Status Badges */
.badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 0.75em;
    font-weight: var(--font-weight-semibold);
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--border-radius-sm);
    margin-left: var(--spacing-xs);
}

.badge-success {
    color: var(--white);
    background-color: var(--success-color);
}

.badge-warning {
    color: var(--text-dark);
    background-color: #ffc107;
}

.badge-danger {
    color: var(--white);
    background-color: var(--error-color);
}

.badge-info {
    color: var(--white);
    background-color: var(--info-color);
}

.verification-status {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.verification-date {
    color: var(--text-light);
    font-size: 0.8em;
    font-style: italic;
}

/* Account Deletion Modal Styles */
.deletion-warning,
.deletion-verification,
.deletion-final,
.deletion-complete {
    text-align: center;
    padding: var(--spacing-lg);
}

.deletion-warning .warning-icon,
.deletion-final .warning-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
}

.deletion-complete .success-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
}

.deletion-warning h4,
.deletion-verification h4,
.deletion-final h4,
.deletion-complete h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-dark);
}

.deletion-warning p,
.deletion-verification p,
.deletion-final p,
.deletion-complete p {
    margin-bottom: var(--spacing-md);
    color: var(--text-light);
}

.deletion-warning ul {
    text-align: left;
    max-width: 300px;
    margin: var(--spacing-md) auto;
    padding-left: var(--spacing-lg);
}

.deletion-warning ul li {
    margin-bottom: var(--spacing-xs);
    color: var(--text-light);
}

.deletion-options {
    margin: var(--spacing-xl) 0;
    text-align: left;
}

.final-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.final-warning .warning-icon {
    color: #856404;
}

.final-warning h4 {
    color: #856404;
}

.final-warning p {
    color: #856404;
    margin-bottom: var(--spacing-sm);
}

/* Danger zone styling */
.danger-zone {
    border: 2px solid #dc3545;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-xl);
    background-color: #fff5f5;
}

.danger-zone h3 {
    color: #dc3545;
    margin-bottom: var(--spacing-md);
}

.btn-danger {
    background-color: #dc3545;
    color: var(--white);
    border-color: #dc3545;
}

.btn-danger:hover,
.btn-danger:focus {
    background-color: #c82333;
    border-color: #bd2130;
    color: var(--white);
    text-decoration: none;
}

.btn-danger:disabled {
    background-color: #dc3545;
    border-color: #dc3545;
    opacity: 0.6;
}

/* Sync Status Indicator */
.sync-status {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-xs) 0;
}

.sync-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.sync-indicator:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.sync-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.sync-indicator.online .sync-dot {
    background-color: var(--success-color);
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
}

.sync-indicator.offline .sync-dot {
    background-color: var(--error-color);
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}

.sync-indicator.syncing .sync-dot {
    background-color: #ffc107;
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.sync-text {
    color: var(--text-light);
    font-weight: var(--font-weight-medium);
}

.sync-indicator.online .sync-text {
    color: var(--success-color);
}

.sync-indicator.offline .sync-text {
    color: var(--error-color);
}

.sync-indicator.syncing .sync-text {
    color: #856404;
}

/* Enhanced Order Tracking Styles */
.order-history-item {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    background: var(--white);
    transition: all var(--transition-fast);
}

.order-history-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.order-id-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.order-id {
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
    color: var(--text-dark);
}

.tracking-number {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    font-family: monospace;
}

.order-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    text-transform: capitalize;
}

.order-status.placed { background-color: #e3f2fd; color: #1976d2; }
.order-status.confirmed { background-color: #f3e5f5; color: #7b1fa2; }
.order-status.preparing { background-color: #fff3e0; color: #f57c00; }
.order-status.ready { background-color: #e8f5e8; color: #388e3c; }
.order-status.out_for_delivery { background-color: #fff8e1; color: #f9a825; }
.order-status.delivered { background-color: #e8f5e8; color: #2e7d32; }

.order-progress {
    margin-bottom: var(--spacing-md);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-xs);
}

.progress-bar.large {
    height: 12px;
    margin-bottom: var(--spacing-md);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    transition: width 0.5s ease;
}

.progress-text {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    font-style: italic;
}

.progress-description {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-top: var(--spacing-xs);
}

.order-details {
    margin-bottom: var(--spacing-md);
}

.order-details p {
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.order-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

/* Order Tracking Modal Styles */
.order-tracking {
    max-width: 600px;
}

.tracking-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.tracking-header h4 {
    margin-bottom: var(--spacing-sm);
}

.tracking-header .tracking-number {
    font-size: var(--font-size-lg);
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

.current-status {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-md);
}

.delivery-countdown {
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
}

.tracking-progress {
    margin-bottom: var(--spacing-xl);
}

.status-steps {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-md);
}

.status-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
}

.status-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 50%;
    right: -50%;
    height: 2px;
    background-color: var(--border-color);
    z-index: 1;
}

.status-step.completed:not(:last-child)::after {
    background-color: var(--success-color);
}

.step-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: var(--border-color);
    border: 3px solid var(--white);
    position: relative;
    z-index: 2;
    margin-bottom: var(--spacing-sm);
}

.status-step.completed .step-indicator {
    background-color: var(--success-color);
}

.status-step.current .step-indicator {
    background-color: var(--primary-color);
    animation: pulse 2s infinite;
}

.step-label {
    font-size: var(--font-size-xs);
    text-align: center;
    color: var(--text-light);
    max-width: 80px;
}

.status-step.completed .step-label,
.status-step.current .step-label {
    color: var(--text-dark);
    font-weight: var(--font-weight-semibold);
}

/* Timeline Styles */
.tracking-timeline {
    margin-bottom: var(--spacing-xl);
}

.timeline {
    position: relative;
    padding-left: var(--spacing-lg);
}

.timeline::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.timeline-marker {
    position: absolute;
    left: -15px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--primary-color);
    border: 3px solid var(--white);
    box-shadow: 0 0 0 2px var(--primary-color);
}

.timeline-content {
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-left: var(--spacing-md);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.timeline-status {
    font-weight: var(--font-weight-semibold);
    color: var(--primary-color);
}

.timeline-time {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.timeline-description {
    margin-bottom: var(--spacing-sm);
    color: var(--text-dark);
}

.timeline-location {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-bottom: var(--spacing-xs);
}

.timeline-timestamp {
    font-size: var(--font-size-xs);
    color: var(--text-light);
    font-style: italic;
    margin: 0;
}

.delivery-estimate {
    background-color: #f8f9fa;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
}

.delivery-estimate h5 {
    margin-bottom: var(--spacing-md);
    color: var(--text-dark);
}

.delivery-estimate p {
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

/* Error Handling Styles */
.error-recovery-modal .modal-content {
    max-width: 500px;
}

.error-recovery-modal .error-message {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    color: #856404;
}

.recovery-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.recovery-actions .btn {
    justify-content: center;
}

.form-restored-alert {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: var(--border-radius-md);
    color: #0c5460;
}

.form-restored-alert .btn {
    margin-left: var(--spacing-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
}

.offline-indicator {
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background-color: #dc3545;
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-lg);
    border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    z-index: 1070;
    box-shadow: var(--shadow-md);
    display: none;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        transform: translateX(-50%) translateY(-100%);
    }
    to {
        transform: translateX(-50%) translateY(0);
    }
}

.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Error states for form fields */
.form-control.error {
    border-color: var(--error-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control.error:focus {
    border-color: var(--error-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.error-message {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.error-message::before {
    content: '⚠️';
    font-size: var(--font-size-xs);
}

/* Success states */
.form-control.success {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.success-message {
    color: var(--success-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.success-message::before {
    content: '✅';
    font-size: var(--font-size-xs);
}

/* Retry button styles */
.retry-btn {
    background-color: #ffc107;
    color: #212529;
    border-color: #ffc107;
}

.retry-btn:hover,
.retry-btn:focus {
    background-color: #e0a800;
    border-color: #d39e00;
    color: #212529;
}

/* Network status indicator */
.network-status {
    position: fixed;
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    box-shadow: var(--shadow-md);
    font-size: var(--font-size-sm);
    z-index: 1060;
    display: none;
}

.network-status.online {
    border-color: var(--success-color);
    color: var(--success-color);
}

.network-status.offline {
    border-color: var(--error-color);
    color: var(--error-color);
}

.network-status.reconnecting {
    border-color: #ffc107;
    color: #856404;
}

/* Enhanced Notification Preferences Styles */
.preference-group {
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--white);
}

.preference-group h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-dark);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-sm);
}

.preference-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.preference-description {
    display: block;
    color: var(--text-light);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    margin-left: var(--spacing-xl);
    line-height: var(--line-height-relaxed);
}

.quiet-hours-settings {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: #f8f9fa;
    border-radius: var(--border-radius-sm);
    border-left: 4px solid var(--primary-color);
}

.quiet-hours-settings .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.preference-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.preference-status {
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.preference-status.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.preference-status.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.preference-status.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.preference-status.warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Checkbox enhancements for preferences */
.preferences-form .checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: background-color var(--transition-fast);
}

.preferences-form .checkbox-label:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.preferences-form .checkmark {
    margin-top: 2px; /* Align with text */
}

/* Form control enhancements for preferences */
.preference-group .form-control {
    max-width: 200px;
}

.preference-group .form-label {
    font-weight: var(--font-weight-medium);
    color: var(--text-dark);
}

/* Responsive design for preferences */
@media (max-width: 768px) {
    .preference-group {
        padding: var(--spacing-md);
    }
    
    .preference-actions {
        flex-direction: column;
    }
    
    .quiet-hours-settings .form-row {
        grid-template-columns: 1fr;
    }
    
    .preference-group .form-control {
        max-width: 100%;
    }
}

/* Cards */
.card {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--background-color);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background-color: var(--background-color);
}

.card-title {
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
}

.card-text {
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
}

.card-img-top {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

/* Navigation */
.navbar {
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.navbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
}

.navbar-brand {
    font-family: var(--font-heading);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    text-decoration: none;
}

.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--spacing-lg);
}

.navbar-nav a {
    color: var(--text-color);
    font-weight: var(--font-weight-semibold);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.navbar-nav a:hover,
.navbar-nav a:focus,
.navbar-nav a.active {
    color: var(--primary-color);
    background-color: var(--primary-light);
    text-decoration: none;
}

.navbar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    color: var(--text-color);
    cursor: pointer;
    padding: var(--spacing-xs);
}

/* Forms */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    color: var(--text-color);
    background-color: var(--white);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    transition: border-color var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 122, 0, 0.1);
}

.form-control.is-invalid {
    border-color: var(--error-color);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--error-color);
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--success-color);
}

/* Password Input */
.password-input {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input .form-control {
    padding-right: 60px;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--text-light);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    height: auto;
    width: auto;
}

.password-toggle:hover {
    color: var(--primary-color);
    background-color: var(--background-color);
}

.password-toggle:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 1px;
}

.password-toggle-icon {
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Form Error States */
.form-error {
    display: none;
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}

.form-help {
    color: var(--text-light);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}

/* Alerts */
.alert {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
    font-weight: var(--font-weight-semibold);
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Loading states */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Badge */
.badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    line-height: 1;
    color: var(--white);
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--border-radius-xl);
}

.badge-primary {
    background-color: var(--primary-color);
}

.badge-success {
    background-color: var(--success-color);
}

.badge-warning {
    background-color: var(--warning-color);
    color: var(--text-color);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-dialog {
    position: relative;
    width: auto;
    max-width: 500px;
    margin: var(--spacing-lg);
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    margin: 0;
    font-size: var(--font-size-xl);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    cursor: pointer;
    padding: 0;
    color: var(--text-muted);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--secondary-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
    padding: var(--spacing-xs);
    line-height: 1;
}

.modal-close:hover {
    color: var(--text-color);
}

.modal-body {
    padding: var(--spacing-lg);
}

/* Badges */
.badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--border-radius-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: var(--spacing-sm);
}

.badge-success {
    background-color: var(--success-color);
    color: var(--white);
}

.badge-warning {
    background-color: var(--warning-color);
    color: var(--white);
}

.badge-danger {
    background-color: var(--error-color);
    color: var(--white);
}

/* Alerts */
.alert {
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
    border: 1px solid transparent;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert p {
    margin-bottom: var(--spacing-sm);
}

.alert p:last-child {
    margin-bottom: 0;
}

/* Badges */
.badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--border-radius-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: var(--spacing-sm);
}

.badge-success {
    background-color: var(--success-color);
    color: var(--white);
}

.badge-warning {
    background-color: var(--warning-color);
    color: var(--white);
}

.badge-danger {
    background-color: var(--error-color);
    color: var(--white);
}

/* Alerts */
.alert {
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
    border: 1px solid transparent;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert p {
    margin-bottom: var(--spacing-sm);
}

.alert p:last-child {
    margin-bottom: 0;
}

/* Verification Status */
.verification-status {
    display: inline-block;
}
