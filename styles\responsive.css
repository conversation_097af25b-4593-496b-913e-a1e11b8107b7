/* Magic Menu - Responsive Styles */

/* Mobile First Approach - Base styles are for mobile */

/* Small Mobile (320px and up) - Base styles already applied */

/* Medium Mobile and up (480px) */
@media (min-width: 480px) {
    .container {
        padding: 0 var(--spacing-lg);
    }
    
    .card-img-top {
        height: 220px;
    }
    
    .btn-lg {
        padding: var(--spacing-lg) var(--spacing-2xl);
    }
}

/* Tablet and up (768px) */
@media (min-width: 768px) {
    /* Typography adjustments */
    h1 {
        font-size: 3rem; /* 48px */
    }
    
    h2 {
        font-size: 2.25rem; /* 36px */
    }
    
    .container {
        padding: 0 var(--spacing-xl);
    }
    
    .section {
        padding: var(--spacing-3xl) 0;
    }
    
    /* Navigation */
    .navbar-nav {
        gap: var(--spacing-xl);
    }
    
    /* Grid layouts */
    .grid-2 {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
    
    .grid-3 {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
    
    /* Cards */
    .card-img-top {
        height: 240px;
    }
    
    /* Forms */
    .form-row {
        display: flex;
        gap: var(--spacing-lg);
    }
    
    .form-row .form-group {
        flex: 1;
    }
    
    /* Modal */
    .modal-dialog {
        max-width: 600px;
    }

    /* Admin Responsive Styles - Tablet */
    .admin-header-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .admin-nav-container {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--spacing-md);
    }

    .admin-nav-link {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }

    .admin-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .data-table {
        font-size: var(--font-size-sm);
    }

    .data-table th,
    .data-table td {
        padding: var(--spacing-sm);
    }

    .action-buttons {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .action-buttons .btn-sm {
        width: 100%;
        text-align: center;
    }

    /* Hide less important columns on tablet */
    .data-table .hide-tablet {
        display: none;
    }

    .modal-dialog {
        margin: var(--spacing-lg);
        max-width: calc(100% - 2 * var(--spacing-lg));
    }

    .order-details-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .form-row {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}

/* Desktop and up (1024px) */
@media (min-width: 1024px) {
    /* Typography */
    h1 {
        font-size: var(--font-size-4xl); /* 40px */
    }
    
    .container {
        max-width: 1200px;
    }
    
    /* Navigation - Show full menu */
    .navbar-toggle {
        display: none;
    }
    
    .navbar-nav {
        display: flex !important;
        flex-direction: row;
        gap: var(--spacing-2xl);
    }
    
    /* Grid layouts */
    .grid-3 {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .grid-4 {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-lg);
    }
    
    /* Hero sections */
    .hero {
        min-height: 60vh;
        display: flex;
        align-items: center;
    }
    
    .hero-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-3xl);
        align-items: center;
    }
    
    /* Cards */
    .card-img-top {
        height: 260px;
    }
    
    /* Two-column layouts */
    .two-column {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-3xl);
        align-items: start;
    }
    
    /* Sidebar layouts */
    .sidebar-layout {
        display: grid;
        grid-template-columns: 300px 1fr;
        gap: var(--spacing-2xl);
    }

    /* Account Page Tablet Styles */
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
    }

    .dashboard-header {
        flex-direction: row;
        text-align: left;
    }

    .quick-actions {
        grid-template-columns: repeat(3, 1fr);
    }

    .auth-container {
        max-width: 600px;
    }

    .auth-form {
        padding: var(--spacing-3xl);
    }
}

/* Large Desktop and up (1200px) */
@media (min-width: 1200px) {
    .container {
        max-width: 1400px;
    }
    
    /* Grid layouts */
    .grid-5 {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: var(--spacing-lg);
    }
    
    .grid-6 {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: var(--spacing-lg);
    }
    
    /* Hero sections */
    .hero {
        min-height: 70vh;
    }
    
    /* Cards */
    .card-img-top {
        height: 280px;
    }
}

/* Mobile Navigation (up to 1023px) */
@media (max-width: 1023px) {
    .navbar-toggle {
        display: block;
    }
    
    .navbar-nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--white);
        box-shadow: var(--shadow-lg);
        flex-direction: column;
        padding: var(--spacing-lg);
        gap: var(--spacing-sm);
    }
    
    .navbar-nav.show {
        display: flex;
    }
    
    .navbar-nav a {
        padding: var(--spacing-md);
        border-radius: var(--border-radius-md);
    }
}

/* Print styles */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    a,
    a:visited {
        text-decoration: underline;
    }
    
    .navbar,
    .btn,
    .modal,
    .alert {
        display: none !important;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-light: #000000;
        --text-muted: #000000;
    }
    
    .card {
        border: 2px solid var(--border-color);
    }
    
    .btn {
        border-width: 2px;
    }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles would go here */
    /* Currently not implemented as per requirements */
}

/* Utility classes for responsive design */
.d-none {
    display: none;
}

.d-block {
    display: block;
}

.d-flex {
    display: flex;
}

.d-grid {
    display: grid;
}

/* Mobile utilities */
@media (max-width: 767px) {
    .d-md-none {
        display: none;
    }

    .d-md-block {
        display: block;
    }

    .text-center-mobile {
        text-align: center;
    }

    .btn-block-mobile {
        width: 100%;
        display: flex;
    }

    /* Admin Mobile Styles */
    .admin-header {
        padding: var(--spacing-lg) 0;
    }

    .admin-header h1 {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-xs);
    }

    .admin-header p {
        font-size: var(--font-size-sm);
    }

    .admin-header-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .admin-user-info {
        flex-direction: column;
        gap: var(--spacing-xs);
        width: 100%;
    }

    .admin-user-name,
    .admin-user-role {
        font-size: var(--font-size-sm);
    }

    .admin-nav {
        margin-bottom: var(--spacing-lg);
    }

    .admin-nav-container {
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-md) 0;
    }

    .admin-nav-link {
        padding: var(--spacing-md);
        text-align: center;
        border-radius: var(--border-radius-sm);
        font-size: var(--font-size-sm);
    }

    .admin-section {
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }

    .admin-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .stat-card {
        padding: var(--spacing-md);
        text-align: center;
    }

    .stat-number {
        font-size: var(--font-size-xl);
    }

    /* Mobile Data Tables */
    .data-table-mobile {
        display: block;
    }

    .data-table-mobile .data-table {
        display: none;
    }

    .mobile-card {
        background-color: var(--white);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
        box-shadow: var(--shadow-sm);
    }

    .mobile-card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--spacing-sm);
    }

    .mobile-card-title {
        font-weight: var(--font-weight-semibold);
        color: var(--secondary-color);
    }

    .mobile-card-subtitle {
        font-size: var(--font-size-sm);
        color: var(--text-light);
        margin-bottom: var(--spacing-xs);
    }

    .mobile-card-content {
        margin-bottom: var(--spacing-md);
    }

    .mobile-card-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-xs) 0;
        border-bottom: 1px solid var(--background-color);
    }

    .mobile-card-row:last-child {
        border-bottom: none;
    }

    .mobile-card-label {
        font-weight: var(--font-weight-semibold);
        color: var(--text-color);
        font-size: var(--font-size-sm);
    }

    .mobile-card-value {
        color: var(--text-color);
        font-size: var(--font-size-sm);
        text-align: right;
    }

    .mobile-card-actions {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .mobile-card-actions .btn {
        width: 100%;
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }

    /* Section Controls Mobile */
    .section-controls {
        margin-bottom: var(--spacing-lg);
    }

    .section-controls .btn {
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }

    .filter-controls {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .filter-controls .form-control {
        width: 100%;
    }

    /* Modal Mobile Styles */
    .modal-overlay {
        padding: var(--spacing-sm);
    }

    .modal-dialog {
        margin: 0;
        max-width: 100%;
        max-height: 100%;
        width: 100%;
        height: 100%;
    }

    .modal-dialog.modal-sm,
    .modal-dialog.modal-lg {
        max-width: 100%;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-md);
    }

    .modal-footer {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .modal-footer .btn {
        width: 100%;
    }

    /* Form Mobile Styles */
    .form-row {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .checkbox-group {
        gap: var(--spacing-md);
    }

    /* Order Details Mobile */
    .order-details-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .info-grid {
        gap: var(--spacing-sm);
    }

    .table-responsive {
        overflow-x: auto;
    }

    .table {
        min-width: 500px;
        font-size: var(--font-size-sm);
    }

    .table th,
    .table td {
        padding: var(--spacing-xs);
        white-space: nowrap;
    }

    /* Timeline Mobile */
    .timeline {
        padding-left: var(--spacing-md);
    }

    .timeline-item {
        padding-left: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }

    .timeline-time {
        font-size: var(--font-size-xs);
    }

    .timeline-content {
        font-size: var(--font-size-sm);
    }

    /* Login Modal Mobile */
    .admin-login-overlay {
        padding: var(--spacing-sm);
    }

    .admin-login-container {
        width: 100%;
        max-width: 100%;
        margin: 0;
    }

    .admin-login-form {
        padding: var(--spacing-lg);
    }

    .demo-credentials {
        font-size: var(--font-size-xs);
        padding: var(--spacing-sm);
    }

    /* Section Controls Mobile */
    .section-controls {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .section-controls-left,
    .section-controls-right {
        width: 100%;
    }

    .section-controls-left {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .section-controls-left .btn {
        width: 100%;
    }

    .section-controls-right {
        align-items: stretch;
    }

    .bulk-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }

    .bulk-actions select,
    .bulk-actions .btn {
        width: 100%;
    }

    /* Dashboard Mobile */
    .dashboard-refresh {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }

    .dashboard-refresh-left,
    .dashboard-refresh-right {
        width: 100%;
    }

    .dashboard-refresh-left {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }

    .dashboard-refresh-left .btn {
        width: 100%;
    }

    .auto-refresh-toggle {
        justify-content: center;
    }

    .quick-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .update-indicator {
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        left: var(--spacing-sm);
        transform: translateY(-100%);
        font-size: var(--font-size-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .update-indicator.show {
        transform: translateY(0);
    }
}

/* Tablet utilities */
@media (min-width: 768px) {
    .d-md-none {
        display: none;
    }
    
    .d-md-block {
        display: block;
    }
    
    .d-md-flex {
        display: flex;
    }
    
    .d-md-grid {
        display: grid;
    }
}

/* Desktop utilities */
@media (min-width: 1024px) {
    .d-lg-none {
        display: none;
    }
    
    .d-lg-block {
        display: block;
    }
    
    .d-lg-flex {
        display: flex;
    }
    
    .d-lg-grid {
        display: grid;
    }

    /* Account Page Responsive Styles */
    .dashboard-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .quick-actions {
        grid-template-columns: repeat(3, 1fr);
    }

    .dashboard-header {
        padding: var(--spacing-2xl);
    }

    .info-item {
        flex-direction: row;
    }

    .order-item {
        flex-direction: row;
        align-items: center;
    }

    .order-info {
        flex-direction: row;
        gap: var(--spacing-lg);
        align-items: center;
    }

    /* Admin Responsive Styles - Desktop */
    .admin-header-content {
        flex-direction: row;
        justify-content: space-between;
    }

    .admin-nav-container {
        justify-content: center;
        flex-wrap: nowrap;
    }

    .admin-stats {
        grid-template-columns: repeat(4, 1fr);
    }

    .data-table {
        font-size: var(--font-size-base);
    }

    .action-buttons {
        flex-direction: row;
        gap: var(--spacing-sm);
    }

    .modal-dialog {
        margin: var(--spacing-2xl);
    }

    .order-details-grid {
        grid-template-columns: 1fr 1fr;
    }

    .form-row {
        flex-direction: row;
    }
}
