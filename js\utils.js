/**
 * Magic Menu - Utility Functions
 * Common utility functions used throughout the application
 */

const Utils = {
    // Format currency (Nigerian Naira)
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-NG', {
            style: 'currency',
            currency: 'NGN',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    },

    // Format number with commas
    formatNumber(number) {
        return number.toLocaleString();
    },

    // Validate email address
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // Validate phone number (Nigerian format)
    isValidPhone(phone) {
        // Remove all non-digit characters
        const cleanPhone = phone.replace(/\D/g, '');

        // Check for Nigerian phone number patterns
        // 11 digits starting with 0 (e.g., 08012345678)
        // 13 digits starting with 234 (e.g., 2348012345678)
        // 14 digits starting with +234 (handled by removing +)
        return /^(0[789][01]\d{8}|234[789][01]\d{8})$/.test(cleanPhone);
    },

    // Format phone number for display
    formatPhone(phone) {
        const cleanPhone = phone.replace(/\D/g, '');

        if (cleanPhone.length === 11 && cleanPhone.startsWith('0')) {
            // Format: 0************
            return cleanPhone.replace(/(\d{4})(\d{3})(\d{4})/, '$1 $2 $3');
        } else if (cleanPhone.length === 13 && cleanPhone.startsWith('234')) {
            // Format: +234 ************
            return cleanPhone.replace(/(\d{3})(\d{3})(\d{3})(\d{4})/, '+$1 $2 $3 $4');
        }

        return phone; // Return original if no pattern matches
    },

    // Sanitize HTML to prevent XSS
    sanitizeHTML(str) {
        const div = document.createElement('div');
        div.textContent = str;
        return div.innerHTML;
    },

    // Escape HTML entities
    escapeHTML(str) {
        const div = document.createElement('div');
        div.appendChild(document.createTextNode(str));
        return div.innerHTML;
    },

    // Generate unique ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    // Deep clone object
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    // Check if element is in viewport
    isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    },

    // Smooth scroll to element
    scrollToElement(element, offset = 0) {
        const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
        const offsetPosition = elementPosition - offset;

        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });
    },

    // Get query parameter from URL
    getQueryParam(param) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(param);
    },

    // Set query parameter in URL
    setQueryParam(param, value) {
        const url = new URL(window.location);
        url.searchParams.set(param, value);
        window.history.pushState({}, '', url);
    },

    // Remove query parameter from URL
    removeQueryParam(param) {
        const url = new URL(window.location);
        url.searchParams.delete(param);
        window.history.pushState({}, '', url);
    },

    // Local storage helpers with error handling and sync support
    storage: {
        set(key, value, options = {}) {
            try {
                localStorage.setItem(key, JSON.stringify(value));

                // Trigger sync if DataSync is available and enabled
                if (typeof DataSync !== 'undefined' && !options.skipSync) {
                    DataSync.syncAcrossTabs(key, value, options);
                }

                return true;
            } catch (error) {
                console.error('Error saving to localStorage:', error);

                // Try fallback storage
                return this.setFallback(key, value);
            }
        },

        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.error('Error reading from localStorage:', error);

                // Try fallback storage
                return this.getFallback(key, defaultValue);
            }
        },

        remove(key, options = {}) {
            try {
                localStorage.removeItem(key);

                // Trigger sync for removal if DataSync is available
                if (typeof DataSync !== 'undefined' && !options.skipSync) {
                    DataSync.syncAcrossTabs(key, null, options);
                }

                return true;
            } catch (error) {
                console.error('Error removing from localStorage:', error);
                return false;
            }
        },

        clear() {
            try {
                localStorage.clear();
                return true;
            } catch (error) {
                console.error('Error clearing localStorage:', error);
                return false;
            }
        },

        // Fallback to sessionStorage if localStorage fails
        setFallback(key, value) {
            try {
                sessionStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('Error saving to sessionStorage:', error);

                // Final fallback to memory storage
                return this.setMemoryFallback(key, value);
            }
        },

        getFallback(key, defaultValue = null) {
            try {
                const item = sessionStorage.getItem(key);
                if (item) return JSON.parse(item);

                // Check memory storage
                return this.getMemoryFallback(key, defaultValue);
            } catch (error) {
                console.error('Error reading from sessionStorage:', error);
                return this.getMemoryFallback(key, defaultValue);
            }
        },

        // Memory storage as final fallback
        memoryStorage: new Map(),

        setMemoryFallback(key, value) {
            try {
                this.memoryStorage.set(key, value);
                return true;
            } catch (error) {
                console.error('Error saving to memory storage:', error);
                return false;
            }
        },

        getMemoryFallback(key, defaultValue = null) {
            return this.memoryStorage.get(key) || defaultValue;
        },

        // Check storage availability
        isStorageAvailable(type = 'localStorage') {
            try {
                const storage = window[type];
                const testKey = '__storage_test__';
                storage.setItem(testKey, 'test');
                storage.removeItem(testKey);
                return true;
            } catch (error) {
                return false;
            }
        },

        // Get storage info
        getStorageInfo() {
            return {
                localStorage: this.isStorageAvailable('localStorage'),
                sessionStorage: this.isStorageAvailable('sessionStorage'),
                memoryStorage: true,
                currentFallback: this.getCurrentStorageType()
            };
        },

        getCurrentStorageType() {
            if (this.isStorageAvailable('localStorage')) return 'localStorage';
            if (this.isStorageAvailable('sessionStorage')) return 'sessionStorage';
            return 'memoryStorage';
        }
    },

    // Date formatting helpers
    formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };

        const formatOptions = { ...defaultOptions, ...options };
        return new Intl.DateTimeFormat('en-NG', formatOptions).format(new Date(date));
    },

    formatTime(date, options = {}) {
        const defaultOptions = {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };

        const formatOptions = { ...defaultOptions, ...options };
        return new Intl.DateTimeFormat('en-NG', formatOptions).format(new Date(date));
    },

    formatDateTime(date) {
        return `${this.formatDate(date)} at ${this.formatTime(date)}`;
    },

    // Calculate time ago
    timeAgo(date) {
        const now = new Date();
        const past = new Date(date);
        const diffInSeconds = Math.floor((now - past) / 1000);

        if (diffInSeconds < 60) return 'just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
        if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
        if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;
        return `${Math.floor(diffInSeconds / 31536000)} years ago`;
    },

    // Capitalize first letter
    capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    },

    // Convert string to title case
    toTitleCase(str) {
        return str.replace(/\w\S*/g, (txt) =>
            txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        );
    },

    // Truncate text
    truncate(str, length, suffix = '...') {
        if (str.length <= length) return str;
        return str.substring(0, length) + suffix;
    },

    // Generate random color
    randomColor() {
        return '#' + Math.floor(Math.random() * 16777215).toString(16);
    },

    // Check if device is mobile
    isMobile() {
        return window.innerWidth <= 768;
    },

    // Check if device is tablet
    isTablet() {
        return window.innerWidth > 768 && window.innerWidth <= 1024;
    },

    // Check if device is desktop
    isDesktop() {
        return window.innerWidth > 1024;
    },

    // Get device type
    getDeviceType() {
        if (this.isMobile()) return 'mobile';
        if (this.isTablet()) return 'tablet';
        return 'desktop';
    },

    // Preload image
    preloadImage(src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = reject;
            img.src = src;
        });
    },

    // Copy text to clipboard
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (error) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                document.body.removeChild(textArea);
                return true;
            } catch (fallbackError) {
                document.body.removeChild(textArea);
                console.error('Failed to copy text:', fallbackError);
                return false;
            }
        }
    },

    // Simple hash function for strings
    hashString(str) {
        let hash = 0;
        if (str.length === 0) return hash;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash;
    }
};







/**
 * Data Synchronization System
 * Handles real-time data sync across tabs and sessions
 */
const DataSync = {
    // Initialize data synchronization
    init() {
        this.setupStorageEventListeners();
        this.setupBroadcastChannel();
        this.startSyncMonitoring();
        this.pendingChanges = new Map();
        this.syncQueue = [];
        this.isOnline = navigator.onLine;
        this.setupNetworkListeners();
    },

    // Setup localStorage event listeners for cross-tab sync
    setupStorageEventListeners() {
        window.addEventListener('storage', (e) => {
            this.handleStorageEvent(e);
        });
    },

    // Setup BroadcastChannel for enhanced cross-tab communication
    setupBroadcastChannel() {
        if (typeof BroadcastChannel !== 'undefined') {
            this.channel = new BroadcastChannel('magic-menu-sync');
            this.channel.addEventListener('message', (event) => {
                this.handleBroadcastMessage(event.data);
            });
        }
    },

    // Setup network connectivity listeners
    setupNetworkListeners() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.processPendingChanges();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
        });
    },

    // Handle storage events from other tabs
    handleStorageEvent(event) {
        if (!event.key || event.newValue === event.oldValue) return;

        const syncableKeys = [
            'user', 'userAddresses', 'userPreferences',
            'lastOrder', 'emailVerifications'
        ];

        if (syncableKeys.includes(event.key)) {
            const changeData = {
                key: event.key,
                oldValue: event.oldValue,
                newValue: event.newValue,
                timestamp: new Date().toISOString(),
                source: 'storage_event'
            };

            this.processDataChange(changeData);
        }
    },

    // Handle broadcast channel messages
    handleBroadcastMessage(data) {
        switch (data.type) {
            case 'DATA_SYNC':
                this.processDataSync(data);
                break;
            case 'CONFLICT_RESOLUTION':
                this.handleConflictResolution(data);
                break;
            case 'SYNC_REQUEST':
                this.handleSyncRequest(data);
                break;
        }
    },

    // Process data changes and sync across tabs
    processDataChange(changeData) {
        try {
            // Check for conflicts
            const conflict = this.detectConflict(changeData);
            if (conflict) {
                this.resolveConflict(conflict, changeData);
                return;
            }

            // Apply change and notify other components
            this.applyDataChange(changeData);
            this.notifyDataChange(changeData);

        } catch (error) {
            console.error('Error processing data change:', error);
            this.handleSyncError(error, changeData);
        }
    },

    // Detect data conflicts
    detectConflict(changeData) {
        const currentValue = Utils.storage.get(changeData.key);
        const currentTimestamp = this.getDataTimestamp(changeData.key);

        if (currentValue && currentTimestamp) {
            const changeTimestamp = new Date(changeData.timestamp);
            const currentTime = new Date(currentTimestamp);

            // Conflict if current data is newer than incoming change
            if (currentTime > changeTimestamp) {
                return {
                    key: changeData.key,
                    currentValue: currentValue,
                    currentTimestamp: currentTimestamp,
                    incomingValue: changeData.newValue,
                    incomingTimestamp: changeData.timestamp
                };
            }
        }

        return null;
    },

    // Resolve data conflicts using timestamp-based priority
    resolveConflict(conflict, changeData) {
        const currentTime = new Date(conflict.currentTimestamp);
        const incomingTime = new Date(conflict.incomingTimestamp);

        // Keep the newer data
        if (incomingTime > currentTime) {
            // Incoming data is newer, apply the change
            this.applyDataChange(changeData);
            this.broadcastConflictResolution({
                key: conflict.key,
                resolvedValue: changeData.newValue,
                resolvedTimestamp: changeData.timestamp,
                reason: 'incoming_newer'
            });
        } else {
            // Current data is newer, broadcast current state
            this.broadcastConflictResolution({
                key: conflict.key,
                resolvedValue: conflict.currentValue,
                resolvedTimestamp: conflict.currentTimestamp,
                reason: 'current_newer'
            });
        }
    },

    // Apply data change to current tab
    applyDataChange(changeData) {
        if (changeData.newValue === null) {
            Utils.storage.remove(changeData.key);
        } else {
            const parsedValue = JSON.parse(changeData.newValue);
            Utils.storage.set(changeData.key, parsedValue);
        }

        // Update timestamp
        this.setDataTimestamp(changeData.key, changeData.timestamp);
    },

    // Notify other components of data changes
    notifyDataChange(changeData) {
        // Dispatch custom event for components to listen to
        const event = new CustomEvent('dataSync', {
            detail: {
                key: changeData.key,
                value: changeData.newValue ? JSON.parse(changeData.newValue) : null,
                timestamp: changeData.timestamp,
                source: changeData.source
            }
        });

        window.dispatchEvent(event);
    },

    // Broadcast conflict resolution
    broadcastConflictResolution(resolution) {
        if (this.channel) {
            this.channel.postMessage({
                type: 'CONFLICT_RESOLUTION',
                resolution: resolution,
                timestamp: new Date().toISOString()
            });
        }
    },

    // Handle conflict resolution messages
    handleConflictResolution(data) {
        const { resolution } = data;

        // Apply resolved value if different from current
        const currentValue = Utils.storage.get(resolution.key);
        if (JSON.stringify(currentValue) !== JSON.stringify(resolution.resolvedValue)) {
            Utils.storage.set(resolution.key, resolution.resolvedValue);
            this.setDataTimestamp(resolution.key, resolution.resolvedTimestamp);

            // Notify components of resolution
            this.notifyDataChange({
                key: resolution.key,
                newValue: JSON.stringify(resolution.resolvedValue),
                timestamp: resolution.resolvedTimestamp,
                source: 'conflict_resolution'
            });
        }
    },

    // Sync data across tabs
    syncAcrossTabs(key, value, options = {}) {
        const timestamp = new Date().toISOString();

        // Store with timestamp
        this.setDataTimestamp(key, timestamp);

        // Queue change if offline
        if (!this.isOnline && options.requiresNetwork) {
            this.queuePendingChange(key, value, timestamp);
            return;
        }

        // Broadcast to other tabs
        if (this.channel) {
            this.channel.postMessage({
                type: 'DATA_SYNC',
                key: key,
                value: value,
                timestamp: timestamp,
                source: 'manual_sync'
            });
        }

        // Trigger storage event for tabs without BroadcastChannel support
        const tempKey = `sync_trigger_${Date.now()}`;
        localStorage.setItem(tempKey, JSON.stringify({
            key: key,
            value: value,
            timestamp: timestamp
        }));
        localStorage.removeItem(tempKey);
    },

    // Process data sync messages
    processDataSync(data) {
        const changeData = {
            key: data.key,
            newValue: JSON.stringify(data.value),
            timestamp: data.timestamp,
            source: data.source
        };

        this.processDataChange(changeData);
    },

    // Queue changes for offline processing
    queuePendingChange(key, value, timestamp) {
        this.pendingChanges.set(key, {
            value: value,
            timestamp: timestamp,
            retryCount: 0
        });

        this.syncQueue.push({
            key: key,
            value: value,
            timestamp: timestamp,
            action: 'sync'
        });
    },

    // Process pending changes when back online
    processPendingChanges() {
        if (this.syncQueue.length === 0) return;

        const processNext = () => {
            if (this.syncQueue.length === 0) return;

            const change = this.syncQueue.shift();
            this.syncAcrossTabs(change.key, change.value);

            // Process next change after short delay
            setTimeout(processNext, 100);
        };

        processNext();
        this.pendingChanges.clear();
    },

    // Handle sync errors with retry logic
    handleSyncError(error, changeData) {
        console.error('Sync error:', error);

        // Add to retry queue
        const retryData = this.pendingChanges.get(changeData.key) || { retryCount: 0 };
        retryData.retryCount++;

        if (retryData.retryCount < 3) {
            this.pendingChanges.set(changeData.key, retryData);

            // Retry after exponential backoff
            const delay = Math.pow(2, retryData.retryCount) * 1000;
            setTimeout(() => {
                this.retrySync(changeData);
            }, delay);
        } else {
            // Max retries reached, notify user
            this.notifyUserOfSyncFailure(changeData);
        }
    },

    // Retry failed sync
    retrySync(changeData) {
        try {
            this.processDataChange(changeData);
            this.pendingChanges.delete(changeData.key);
        } catch (error) {
            this.handleSyncError(error, changeData);
        }
    },

    // Notify user of sync failure
    notifyUserOfSyncFailure(changeData) {
        if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
            MagicMenu.showToast(
                `Failed to sync ${changeData.key}. Some changes may not be saved.`,
                'error'
            );
        }
    },

    // Start monitoring for sync issues
    startSyncMonitoring() {
        setInterval(() => {
            this.checkSyncHealth();
        }, 30000); // Check every 30 seconds
    },

    // Check sync health and resolve issues
    checkSyncHealth() {
        // Check for stale data
        this.cleanupStaleData();

        // Process any pending changes
        if (this.isOnline && this.syncQueue.length > 0) {
            this.processPendingChanges();
        }
    },

    // Clean up stale data and timestamps
    cleanupStaleData() {
        const timestamps = Utils.storage.get('dataTimestamps', {});
        const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

        Object.keys(timestamps).forEach(key => {
            if (new Date(timestamps[key]) < oneWeekAgo) {
                delete timestamps[key];
            }
        });

        Utils.storage.set('dataTimestamps', timestamps);
    },

    // Get data timestamp
    getDataTimestamp(key) {
        const timestamps = Utils.storage.get('dataTimestamps', {});
        return timestamps[key];
    },

    // Set data timestamp
    setDataTimestamp(key, timestamp) {
        const timestamps = Utils.storage.get('dataTimestamps', {});
        timestamps[key] = timestamp;
        Utils.storage.set('dataTimestamps', timestamps);
    },

    // Manual sync trigger for specific data
    triggerSync(key) {
        const value = Utils.storage.get(key);
        if (value) {
            this.syncAcrossTabs(key, value);
        }
    },

    // Get sync status
    getSyncStatus() {
        return {
            isOnline: this.isOnline,
            pendingChanges: this.pendingChanges.size,
            queuedItems: this.syncQueue.length,
            lastSyncCheck: new Date().toISOString()
        };
    }
};

/**
 * Order Tracking System
 * Handles comprehensive order tracking with status progression and timeline
 */
const OrderTracking = {
    // Order status progression
    ORDER_STATUSES: [
        { key: 'placed', label: 'Order Placed', description: 'Your order has been received and is being processed' },
        { key: 'confirmed', label: 'Order Confirmed', description: 'Your order has been confirmed and payment processed' },
        { key: 'preparing', label: 'Preparing', description: 'Your delicious meal is being prepared by our chefs' },
        { key: 'ready', label: 'Ready for Pickup', description: 'Your order is ready and waiting for delivery pickup' },
        { key: 'out_for_delivery', label: 'Out for Delivery', description: 'Your order is on its way to you' },
        { key: 'delivered', label: 'Delivered', description: 'Your order has been successfully delivered' }
    ],

    // Create comprehensive order with tracking
    createOrderWithTracking(orderData) {
        const orderId = orderData.orderNumber || this.generateOrderId();
        const now = new Date().toISOString();

        const trackingData = {
            orderId: orderId,
            status: 'placed',
            timeline: [
                {
                    status: 'placed',
                    timestamp: now,
                    description: 'Order placed successfully',
                    location: 'Magic Menu Restaurant',
                    automated: true
                }
            ],
            estimatedDelivery: this.calculateEstimatedDelivery(orderData),
            actualDelivery: null,
            trackingNumber: this.generateTrackingNumber(),
            customerInfo: orderData.customerInfo,
            items: orderData.items,
            totals: orderData.totals,
            deliveryAddress: orderData.customerInfo?.address,
            specialInstructions: orderData.customerInfo?.instructions,
            createdAt: now,
            updatedAt: now
        };

        // Store tracking data
        Utils.storage.set(`orderTracking_${orderId}`, trackingData);

        // Update order list
        this.addToOrderList(orderId);

        // Start automated status progression
        this.startAutomatedProgression(orderId);

        return trackingData;
    },

    // Generate order ID
    generateOrderId() {
        return 'MM' + Date.now().toString().slice(-6);
    },

    // Generate tracking number
    generateTrackingNumber() {
        return 'TRK' + Date.now().toString(36).toUpperCase() + Math.random().toString(36).substr(2, 4).toUpperCase();
    },

    // Calculate estimated delivery time
    calculateEstimatedDelivery(orderData) {
        const baseTime = 30; // Base preparation time in minutes
        const itemCount = orderData.items?.length || 1;
        const additionalTime = Math.min(itemCount * 5, 20); // Max 20 extra minutes
        const totalMinutes = baseTime + additionalTime;

        return new Date(Date.now() + totalMinutes * 60 * 1000).toISOString();
    },

    // Update order status
    updateOrderStatus(orderId, newStatus, customDescription = null, location = null) {
        const trackingData = Utils.storage.get(`orderTracking_${orderId}`);
        if (!trackingData) {
            return { success: false, error: 'Order not found' };
        }

        const statusInfo = this.ORDER_STATUSES.find(s => s.key === newStatus);
        if (!statusInfo) {
            return { success: false, error: 'Invalid status' };
        }

        const now = new Date().toISOString();

        // Add timeline entry
        const timelineEntry = {
            status: newStatus,
            timestamp: now,
            description: customDescription || statusInfo.description,
            location: location || this.getDefaultLocation(newStatus),
            automated: !customDescription
        };

        trackingData.timeline.push(timelineEntry);
        trackingData.status = newStatus;
        trackingData.updatedAt = now;

        // Update estimated delivery if needed
        if (newStatus === 'out_for_delivery') {
            trackingData.estimatedDelivery = this.recalculateDeliveryTime(trackingData);
        }

        // Set actual delivery time
        if (newStatus === 'delivered') {
            trackingData.actualDelivery = now;
        }

        // Save updated tracking data
        Utils.storage.set(`orderTracking_${orderId}`, trackingData);

        // Notify user of status change
        this.notifyStatusChange(trackingData, statusInfo);

        // Sync across tabs
        if (typeof DataSync !== 'undefined') {
            DataSync.syncAcrossTabs(`orderTracking_${orderId}`, trackingData);
        }

        return { success: true, trackingData: trackingData };
    },

    // Get default location for status
    getDefaultLocation(status) {
        const locations = {
            placed: 'Magic Menu Restaurant',
            confirmed: 'Magic Menu Restaurant',
            preparing: 'Magic Menu Kitchen',
            ready: 'Magic Menu Restaurant',
            out_for_delivery: 'On Route',
            delivered: 'Customer Location'
        };
        return locations[status] || 'Magic Menu Restaurant';
    },

    // Recalculate delivery time when out for delivery
    recalculateDeliveryTime(trackingData) {
        const deliveryTime = 15; // 15 minutes for delivery
        return new Date(Date.now() + deliveryTime * 60 * 1000).toISOString();
    },

    // Start automated status progression
    startAutomatedProgression(orderId) {
        const progressionSchedule = [
            { status: 'confirmed', delay: 2 * 60 * 1000 }, // 2 minutes
            { status: 'preparing', delay: 5 * 60 * 1000 }, // 5 minutes
            { status: 'ready', delay: 20 * 60 * 1000 }, // 20 minutes
            { status: 'out_for_delivery', delay: 25 * 60 * 1000 }, // 25 minutes
            { status: 'delivered', delay: 40 * 60 * 1000 } // 40 minutes
        ];

        progressionSchedule.forEach(({ status, delay }) => {
            setTimeout(() => {
                const currentTracking = Utils.storage.get(`orderTracking_${orderId}`);
                if (currentTracking && currentTracking.status !== 'delivered') {
                    this.updateOrderStatus(orderId, status);
                }
            }, delay);
        });
    },

    // Notify user of status change
    notifyStatusChange(trackingData, statusInfo) {
        if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
            const message = `Order ${trackingData.orderId}: ${statusInfo.label}`;
            MagicMenu.showToast(message, 'info', 4000);
        }

        // Browser notification if supported and permitted
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(`Magic Menu - ${statusInfo.label}`, {
                body: statusInfo.description,
                icon: '/images/icons/favicon.ico',
                tag: `order_${trackingData.orderId}`
            });
        }
    },

    // Get order tracking data
    getOrderTracking(orderId) {
        return Utils.storage.get(`orderTracking_${orderId}`);
    },

    // Get all orders for current user
    getUserOrders() {
        const user = Utils.storage.get('user');
        if (!user) return [];

        const orderList = Utils.storage.get('userOrderList', []);
        return orderList.map(orderId => {
            const tracking = this.getOrderTracking(orderId);
            return tracking;
        }).filter(Boolean).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    },

    // Add order to user's order list
    addToOrderList(orderId) {
        const orderList = Utils.storage.get('userOrderList', []);
        if (!orderList.includes(orderId)) {
            orderList.unshift(orderId); // Add to beginning
            Utils.storage.set('userOrderList', orderList);
        }
    },

    // Get order status progress percentage
    getStatusProgress(status) {
        const statusIndex = this.ORDER_STATUSES.findIndex(s => s.key === status);
        return statusIndex >= 0 ? ((statusIndex + 1) / this.ORDER_STATUSES.length) * 100 : 0;
    },

    // Get next status in progression
    getNextStatus(currentStatus) {
        const currentIndex = this.ORDER_STATUSES.findIndex(s => s.key === currentStatus);
        if (currentIndex >= 0 && currentIndex < this.ORDER_STATUSES.length - 1) {
            return this.ORDER_STATUSES[currentIndex + 1];
        }
        return null;
    },

    // Format timeline for display
    formatTimeline(timeline) {
        return timeline.map(entry => ({
            ...entry,
            formattedTime: Utils.formatDateTime(entry.timestamp),
            timeAgo: Utils.timeAgo(entry.timestamp)
        }));
    },

    // Get delivery time estimate
    getDeliveryEstimate(trackingData) {
        if (trackingData.actualDelivery) {
            return {
                type: 'delivered',
                time: trackingData.actualDelivery,
                formatted: Utils.formatDateTime(trackingData.actualDelivery)
            };
        }

        if (trackingData.estimatedDelivery) {
            const estimatedTime = new Date(trackingData.estimatedDelivery);
            const now = new Date();
            const isOverdue = estimatedTime < now;

            return {
                type: isOverdue ? 'overdue' : 'estimated',
                time: trackingData.estimatedDelivery,
                formatted: Utils.formatDateTime(trackingData.estimatedDelivery),
                minutesRemaining: isOverdue ? 0 : Math.ceil((estimatedTime - now) / (1000 * 60))
            };
        }

        return null;
    },

    // Request notification permission
    requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    MagicMenu.showToast('Order notifications enabled!', 'success');
                }
            });
        }
    },

    // Clean up old orders (keep last 50)
    cleanupOldOrders() {
        const orderList = Utils.storage.get('userOrderList', []);
        if (orderList.length > 50) {
            const toRemove = orderList.slice(50);
            toRemove.forEach(orderId => {
                Utils.storage.remove(`orderTracking_${orderId}`);
            });
            Utils.storage.set('userOrderList', orderList.slice(0, 50));
        }
    }
};

/**
 * Error Handling Framework
 * Comprehensive error handling with recovery mechanisms
 */
const ErrorHandler = {
    // Error types
    ERROR_TYPES: {
        NETWORK: 'network',
        STORAGE: 'storage',
        VALIDATION: 'validation',
        SYNC: 'sync',
        PERMISSION: 'permission',
        TIMEOUT: 'timeout',
        UNKNOWN: 'unknown'
    },

    // Error severity levels
    SEVERITY: {
        LOW: 'low',
        MEDIUM: 'medium',
        HIGH: 'high',
        CRITICAL: 'critical'
    },

    // Initialize error handling
    init() {
        this.setupGlobalErrorHandlers();
        this.errorLog = [];
        this.retryQueue = new Map();
        this.formDataBackup = new Map();
        this.networkRetryCount = new Map();
        this.maxRetries = 3;
        this.retryDelay = 1000; // Base delay in ms
    },

    // Setup global error handlers
    setupGlobalErrorHandlers() {
        // Global JavaScript error handler
        window.addEventListener('error', (event) => {
            this.handleGlobalError({
                type: this.ERROR_TYPES.UNKNOWN,
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error,
                severity: this.SEVERITY.HIGH
            });
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            this.handleGlobalError({
                type: this.ERROR_TYPES.UNKNOWN,
                message: 'Unhandled Promise Rejection',
                reason: event.reason,
                severity: this.SEVERITY.HIGH
            });
        });

        // Network error detection
        window.addEventListener('offline', () => {
            this.handleNetworkError('offline');
        });

        window.addEventListener('online', () => {
            this.handleNetworkRecovery();
        });
    },

    // Handle global errors
    handleGlobalError(errorInfo) {
        this.logError(errorInfo);

        // Don't show user notifications for low severity errors
        if (errorInfo.severity === this.SEVERITY.LOW) {
            return;
        }

        // Show user-friendly error message
        const userMessage = this.getUserFriendlyMessage(errorInfo);
        if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
            MagicMenu.showToast(userMessage, 'error');
        }

        // Attempt recovery if possible
        this.attemptErrorRecovery(errorInfo);
    },

    // Handle network errors
    handleNetworkErrors(operation, retryCallback) {
        return new Promise((resolve, reject) => {
            const attemptOperation = async (attempt = 1) => {
                try {
                    const result = await operation();
                    // Clear retry count on success
                    this.networkRetryCount.delete(operation.name);
                    resolve(result);
                } catch (error) {
                    const errorInfo = {
                        type: this.ERROR_TYPES.NETWORK,
                        message: error.message,
                        operation: operation.name,
                        attempt: attempt,
                        severity: this.SEVERITY.MEDIUM
                    };

                    this.logError(errorInfo);

                    if (attempt < this.maxRetries && navigator.onLine) {
                        // Exponential backoff
                        const delay = this.retryDelay * Math.pow(2, attempt - 1);

                        if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                            MagicMenu.showToast(`Network error. Retrying in ${delay / 1000}s... (${attempt}/${this.maxRetries})`, 'warning');
                        }

                        setTimeout(() => {
                            attemptOperation(attempt + 1);
                        }, delay);
                    } else {
                        // Max retries reached or offline
                        const finalError = {
                            ...errorInfo,
                            severity: this.SEVERITY.HIGH,
                            maxRetriesReached: true
                        };

                        this.handleGlobalError(finalError);
                        reject(error);
                    }
                }
            };

            attemptOperation();
        });
    },

    // Handle network recovery
    handleNetworkRecovery() {
        if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
            MagicMenu.showToast('Connection restored. Syncing data...', 'success');
        }

        // Retry failed operations
        this.retryFailedOperations();

        // Trigger data sync
        if (typeof DataSync !== 'undefined') {
            DataSync.processPendingChanges();
        }
    },

    // Implement fallback storage
    implementFallbackStorage() {
        const storageInfo = Utils.storage.getStorageInfo();

        if (!storageInfo.localStorage) {
            this.logError({
                type: this.ERROR_TYPES.STORAGE,
                message: 'localStorage not available, using sessionStorage fallback',
                severity: this.SEVERITY.MEDIUM
            });

            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Using temporary storage. Data may not persist between sessions.', 'warning');
            }
        }

        if (!storageInfo.sessionStorage) {
            this.logError({
                type: this.ERROR_TYPES.STORAGE,
                message: 'sessionStorage not available, using memory fallback',
                severity: this.SEVERITY.HIGH
            });

            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Storage limited. Data will be lost when page is refreshed.', 'error');
            }
        }

        return storageInfo.currentFallback;
    },

    // Preserve user input during errors
    preserveUserInput(formElement, formData) {
        if (!formElement || !formData) return;

        const formId = formElement.id || formElement.dataset.formType || 'unknown_form';
        const preservedData = {
            data: formData,
            timestamp: new Date().toISOString(),
            formId: formId
        };

        this.formDataBackup.set(formId, preservedData);

        // Store in localStorage as backup
        try {
            Utils.storage.set(`formBackup_${formId}`, preservedData);
        } catch (error) {
            this.logError({
                type: this.ERROR_TYPES.STORAGE,
                message: 'Failed to backup form data',
                error: error,
                severity: this.SEVERITY.MEDIUM
            });
        }
    },

    // Restore user input after error recovery
    restoreUserInput(formElement) {
        if (!formElement) return false;

        const formId = formElement.id || formElement.dataset.formType || 'unknown_form';
        let preservedData = this.formDataBackup.get(formId);

        // Try to get from localStorage if not in memory
        if (!preservedData) {
            preservedData = Utils.storage.get(`formBackup_${formId}`);
        }

        if (preservedData && preservedData.data) {
            // Restore form fields
            Object.keys(preservedData.data).forEach(fieldName => {
                const field = formElement.querySelector(`[name="${fieldName}"]`);
                if (field) {
                    if (field.type === 'checkbox' || field.type === 'radio') {
                        field.checked = preservedData.data[fieldName] === 'on' || preservedData.data[fieldName] === true;
                    } else {
                        field.value = preservedData.data[fieldName];
                    }
                }
            });

            // Show restoration message
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Form data restored from backup', 'info');
            }

            // Clean up backup
            this.formDataBackup.delete(formId);
            Utils.storage.remove(`formBackup_${formId}`);

            return true;
        }

        return false;
    },

    // Show recovery options to user
    showRecoveryOptions(errorInfo) {
        const recoveryActions = this.getRecoveryActions(errorInfo);

        if (recoveryActions.length === 0) return;

        // Create recovery modal
        const modal = document.createElement('div');
        modal.className = 'modal error-recovery-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Something went wrong</h3>
                </div>
                <div class="modal-body">
                    <p class="error-message">${this.getUserFriendlyMessage(errorInfo)}</p>
                    <div class="recovery-actions">
                        ${recoveryActions.map(action => `
                            <button type="button" class="btn ${action.primary ? 'btn-primary' : 'btn-secondary'}" 
                                    data-action="${action.key}">
                                ${action.label}
                            </button>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.classList.remove('hidden');

        // Add event listeners
        modal.querySelectorAll('[data-action]').forEach(button => {
            button.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                this.executeRecoveryAction(action, errorInfo);
                document.body.removeChild(modal);
            });
        });

        // Auto-close after 30 seconds
        setTimeout(() => {
            if (modal.parentNode) {
                document.body.removeChild(modal);
            }
        }, 30000);
    },

    // Get recovery actions for error type
    getRecoveryActions(errorInfo) {
        const actions = [];

        switch (errorInfo.type) {
            case this.ERROR_TYPES.NETWORK:
                actions.push(
                    { key: 'retry', label: 'Try Again', primary: true },
                    { key: 'offline_mode', label: 'Continue Offline', primary: false },
                    { key: 'refresh', label: 'Refresh Page', primary: false }
                );
                break;

            case this.ERROR_TYPES.STORAGE:
                actions.push(
                    { key: 'clear_storage', label: 'Clear Storage', primary: true },
                    { key: 'refresh', label: 'Refresh Page', primary: false }
                );
                break;

            case this.ERROR_TYPES.SYNC:
                actions.push(
                    { key: 'manual_sync', label: 'Manual Sync', primary: true },
                    { key: 'reset_sync', label: 'Reset Sync', primary: false }
                );
                break;

            default:
                actions.push(
                    { key: 'refresh', label: 'Refresh Page', primary: true },
                    { key: 'report_error', label: 'Report Issue', primary: false }
                );
        }

        return actions;
    },

    // Execute recovery action
    executeRecoveryAction(action, errorInfo) {
        switch (action) {
            case 'retry':
                this.retryFailedOperation(errorInfo);
                break;

            case 'refresh':
                window.location.reload();
                break;

            case 'offline_mode':
                this.enableOfflineMode();
                break;

            case 'clear_storage':
                this.clearStorageWithConfirmation();
                break;

            case 'manual_sync':
                if (typeof DataSync !== 'undefined') {
                    DataSync.processPendingChanges();
                }
                break;

            case 'reset_sync':
                this.resetSyncData();
                break;

            case 'report_error':
                this.reportError(errorInfo);
                break;
        }
    },

    // Attempt automatic error recovery
    attemptErrorRecovery(errorInfo) {
        switch (errorInfo.type) {
            case this.ERROR_TYPES.STORAGE:
                this.implementFallbackStorage();
                break;

            case this.ERROR_TYPES.NETWORK:
                if (navigator.onLine) {
                    this.scheduleRetry(errorInfo);
                }
                break;

            case this.ERROR_TYPES.SYNC:
                if (typeof DataSync !== 'undefined') {
                    DataSync.handleSyncError(errorInfo);
                }
                break;
        }
    },

    // Get user-friendly error message
    getUserFriendlyMessage(errorInfo) {
        const messages = {
            [this.ERROR_TYPES.NETWORK]: 'Network connection issue. Please check your internet connection.',
            [this.ERROR_TYPES.STORAGE]: 'Storage issue. Your browser may be running low on space.',
            [this.ERROR_TYPES.VALIDATION]: 'Please check your input and try again.',
            [this.ERROR_TYPES.SYNC]: 'Data synchronization issue. Some changes may not be saved.',
            [this.ERROR_TYPES.PERMISSION]: 'Permission denied. Please check your browser settings.',
            [this.ERROR_TYPES.TIMEOUT]: 'Request timed out. Please try again.',
            [this.ERROR_TYPES.UNKNOWN]: 'An unexpected error occurred. Please try refreshing the page.'
        };

        return messages[errorInfo.type] || messages[this.ERROR_TYPES.UNKNOWN];
    },

    // Log error for debugging
    logError(errorInfo) {
        const logEntry = {
            ...errorInfo,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            id: Utils.generateId()
        };

        this.errorLog.push(logEntry);
        console.error('ErrorHandler:', logEntry);

        // Keep only last 100 errors
        if (this.errorLog.length > 100) {
            this.errorLog = this.errorLog.slice(-100);
        }

        // Store critical errors
        if (errorInfo.severity === this.SEVERITY.CRITICAL) {
            try {
                const criticalErrors = Utils.storage.get('criticalErrors', []);
                criticalErrors.push(logEntry);
                Utils.storage.set('criticalErrors', criticalErrors.slice(-10)); // Keep last 10
            } catch (e) {
                // Storage error while logging - use console only
                console.error('Failed to store critical error:', e);
            }
        }
    },

    // Retry failed operations
    retryFailedOperations() {
        this.retryQueue.forEach((operation, key) => {
            if (operation.retryCount < this.maxRetries) {
                operation.retryCount++;
                this.scheduleRetry(operation);
            } else {
                this.retryQueue.delete(key);
            }
        });
    },

    // Schedule retry with exponential backoff
    scheduleRetry(errorInfo) {
        const delay = this.retryDelay * Math.pow(2, errorInfo.attempt || 0);

        setTimeout(() => {
            if (errorInfo.retryCallback && typeof errorInfo.retryCallback === 'function') {
                errorInfo.retryCallback();
            }
        }, delay);
    },

    // Enable offline mode
    enableOfflineMode() {
        if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
            MagicMenu.showToast('Offline mode enabled. Changes will sync when connection is restored.', 'info');
        }

        // Add offline indicator
        this.showOfflineIndicator();
    },

    // Show offline indicator
    showOfflineIndicator() {
        let indicator = document.getElementById('offline-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'offline-indicator';
            indicator.className = 'offline-indicator';
            indicator.innerHTML = '📡 Offline Mode';
            document.body.appendChild(indicator);
        }
        indicator.style.display = 'block';
    },

    // Hide offline indicator
    hideOfflineIndicator() {
        const indicator = document.getElementById('offline-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    },

    // Clear storage with confirmation
    clearStorageWithConfirmation() {
        if (confirm('This will clear all stored data. Are you sure?')) {
            try {
                localStorage.clear();
                sessionStorage.clear();
                if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                    MagicMenu.showToast('Storage cleared successfully', 'success');
                }
                setTimeout(() => window.location.reload(), 1000);
            } catch (error) {
                this.logError({
                    type: this.ERROR_TYPES.STORAGE,
                    message: 'Failed to clear storage',
                    error: error,
                    severity: this.SEVERITY.HIGH
                });
            }
        }
    },

    // Reset sync data
    resetSyncData() {
        try {
            Utils.storage.remove('dataTimestamps');
            if (typeof DataSync !== 'undefined') {
                DataSync.pendingChanges.clear();
                DataSync.syncQueue = [];
            }
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Sync data reset successfully', 'success');
            }
        } catch (error) {
            this.logError({
                type: this.ERROR_TYPES.SYNC,
                message: 'Failed to reset sync data',
                error: error,
                severity: this.SEVERITY.MEDIUM
            });
        }
    },

    // Report error (placeholder for actual reporting)
    reportError(errorInfo) {
        const reportData = {
            error: errorInfo,
            userAgent: navigator.userAgent,
            url: window.location.href,
            timestamp: new Date().toISOString()
        };

        console.log('Error report:', reportData);

        if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
            MagicMenu.showToast('Error report generated. Check console for details.', 'info');
        }
    },

    // Get error statistics
    getErrorStats() {
        const stats = {
            total: this.errorLog.length,
            byType: {},
            bySeverity: {},
            recent: this.errorLog.slice(-10)
        };

        this.errorLog.forEach(error => {
            stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
            stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
        });

        return stats;
    }
};

/**
 * Notification Preferences System
 * Handles granular notification preferences with immediate saving and sync
 */
const NotificationPreferences = {
    // Default preferences
    DEFAULT_PREFERENCES: {
        // Order notifications
        'order-placed': true,
        'order-preparing': true,
        'order-delivery': true,

        // Communication channels
        'email-notifications': true,
        'sms-notifications': true,
        'browser-notifications': false,

        // Marketing
        'newsletter': false,
        'promotional-offers': false,
        'new-menu-items': false,

        // Frequency settings
        'email-frequency': 'daily',
        'marketing-frequency': 'weekly',

        // Quiet hours
        'enable-quiet-hours': false,
        'quiet-start': '22:00',
        'quiet-end': '08:00'
    },

    // Initialize preferences system
    init() {
        this.loadPreferences();
        this.setupEventListeners();
        this.requestBrowserNotificationPermission();
    },

    // Setup event listeners for preference changes
    setupEventListeners() {
        // Save preferences on change
        document.addEventListener('change', (e) => {
            if (e.target.closest('.preferences-form')) {
                this.handlePreferenceChange(e.target);
            }
        });

        // Handle quiet hours toggle
        const quietHoursCheckbox = document.querySelector('input[name="enable-quiet-hours"]');
        if (quietHoursCheckbox) {
            quietHoursCheckbox.addEventListener('change', (e) => {
                const settings = document.getElementById('quiet-hours-settings');
                if (settings) {
                    settings.style.display = e.target.checked ? 'block' : 'none';
                }
            });
        }

        // Handle browser notifications permission
        const browserNotificationsCheckbox = document.querySelector('input[name="browser-notifications"]');
        if (browserNotificationsCheckbox) {
            browserNotificationsCheckbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.requestBrowserNotificationPermission();
                }
            });
        }
    },

    // Handle individual preference changes
    handlePreferenceChange(element) {
        const preferences = this.getPreferences();
        const name = element.name;
        let value;

        if (element.type === 'checkbox') {
            value = element.checked;
        } else {
            value = element.value;
        }

        // Update preference
        preferences[name] = value;

        // Save immediately
        this.savePreferences(preferences);

        // Show status feedback
        this.showPreferenceStatus('Preference updated', 'success');

        // Sync across tabs
        if (typeof DataSync !== 'undefined') {
            DataSync.syncAcrossTabs('userPreferences', preferences);
        }
    },

    // Load preferences from storage
    loadPreferences() {
        const stored = Utils.storage.get('userPreferences', {});
        const preferences = { ...this.DEFAULT_PREFERENCES, ...stored };

        // Apply preferences to UI
        this.applyPreferencesToUI(preferences);

        return preferences;
    },

    // Get current preferences
    getPreferences() {
        return Utils.storage.get('userPreferences', this.DEFAULT_PREFERENCES);
    },

    // Save preferences to storage
    savePreferences(preferences) {
        try {
            // Validate preferences
            const validatedPreferences = this.validatePreferences(preferences);

            // Save to storage
            Utils.storage.set('userPreferences', validatedPreferences);

            // Update timestamp
            validatedPreferences.lastUpdated = new Date().toISOString();

            return { success: true, preferences: validatedPreferences };
        } catch (error) {
            console.error('Error saving preferences:', error);

            if (typeof ErrorHandler !== 'undefined') {
                ErrorHandler.logError({
                    type: ErrorHandler.ERROR_TYPES.STORAGE,
                    message: 'Failed to save notification preferences',
                    error: error,
                    severity: ErrorHandler.SEVERITY.MEDIUM
                });
            }

            this.showPreferenceStatus('Failed to save preferences', 'error');
            return { success: false, error: error.message };
        }
    },

    // Validate preferences object
    validatePreferences(preferences) {
        const validated = {};

        // Validate each preference against defaults
        Object.keys(this.DEFAULT_PREFERENCES).forEach(key => {
            if (preferences.hasOwnProperty(key)) {
                const defaultValue = this.DEFAULT_PREFERENCES[key];
                const userValue = preferences[key];

                // Type validation
                if (typeof defaultValue === typeof userValue) {
                    validated[key] = userValue;
                } else {
                    validated[key] = defaultValue;
                }
            } else {
                validated[key] = this.DEFAULT_PREFERENCES[key];
            }
        });

        // Validate time format for quiet hours
        if (validated['quiet-start'] && !this.isValidTimeFormat(validated['quiet-start'])) {
            validated['quiet-start'] = this.DEFAULT_PREFERENCES['quiet-start'];
        }

        if (validated['quiet-end'] && !this.isValidTimeFormat(validated['quiet-end'])) {
            validated['quiet-end'] = this.DEFAULT_PREFERENCES['quiet-end'];
        }

        return validated;
    },

    // Validate time format (HH:MM)
    isValidTimeFormat(time) {
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
    },

    // Apply preferences to UI elements
    applyPreferencesToUI(preferences) {
        Object.keys(preferences).forEach(key => {
            const element = document.querySelector(`[name="${key}"]`);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = preferences[key];

                    // Handle quiet hours visibility
                    if (key === 'enable-quiet-hours') {
                        const settings = document.getElementById('quiet-hours-settings');
                        if (settings) {
                            settings.style.display = preferences[key] ? 'block' : 'none';
                        }
                    }
                } else {
                    element.value = preferences[key];
                }
            }
        });
    },

    // Reset preferences to defaults
    resetToDefaults() {
        if (confirm('Reset all notification preferences to default settings?')) {
            this.savePreferences(this.DEFAULT_PREFERENCES);
            this.applyPreferencesToUI(this.DEFAULT_PREFERENCES);
            this.showPreferenceStatus('Preferences reset to defaults', 'success');

            // Sync across tabs
            if (typeof DataSync !== 'undefined') {
                DataSync.syncAcrossTabs('userPreferences', this.DEFAULT_PREFERENCES);
            }
        }
    },

    // Show preference status message
    showPreferenceStatus(message, type = 'info') {
        const statusElement = document.getElementById('preference-status');
        const statusText = statusElement?.querySelector('.status-text');

        if (statusElement && statusText) {
            statusText.textContent = message;
            statusElement.className = `preference-status ${type}`;
            statusElement.style.display = 'block';

            // Auto-hide after 3 seconds
            setTimeout(() => {
                statusElement.style.display = 'none';
            }, 3000);
        }

        // Also show toast notification
        if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
            MagicMenu.showToast(message, type);
        }
    },

    // Request browser notification permission
    requestBrowserNotificationPermission() {
        if ('Notification' in window) {
            if (Notification.permission === 'default') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        this.showPreferenceStatus('Browser notifications enabled', 'success');
                    } else if (permission === 'denied') {
                        // Uncheck the browser notifications checkbox
                        const checkbox = document.querySelector('input[name="browser-notifications"]');
                        if (checkbox) {
                            checkbox.checked = false;
                            this.handlePreferenceChange(checkbox);
                        }
                        this.showPreferenceStatus('Browser notifications blocked', 'warning');
                    }
                });
            } else if (Notification.permission === 'denied') {
                // Uncheck the browser notifications checkbox
                const checkbox = document.querySelector('input[name="browser-notifications"]');
                if (checkbox) {
                    checkbox.checked = false;
                }
                this.showPreferenceStatus('Browser notifications are blocked. Please enable them in your browser settings.', 'warning');
            }
        } else {
            // Browser doesn't support notifications
            const checkbox = document.querySelector('input[name="browser-notifications"]');
            if (checkbox) {
                checkbox.disabled = true;
                checkbox.checked = false;
            }
        }
    },

    // Check if notifications should be sent based on preferences
    shouldSendNotification(type, channel = 'email') {
        const preferences = this.getPreferences();

        // Check if the notification type is enabled
        if (!preferences[type]) {
            return false;
        }

        // Check if the communication channel is enabled
        const channelKey = `${channel}-notifications`;
        if (preferences.hasOwnProperty(channelKey) && !preferences[channelKey]) {
            return false;
        }

        // Check quiet hours for non-urgent notifications
        if (this.isQuietHours() && !this.isUrgentNotification(type)) {
            return false;
        }

        return true;
    },

    // Check if current time is within quiet hours
    isQuietHours() {
        const preferences = this.getPreferences();

        if (!preferences['enable-quiet-hours']) {
            return false;
        }

        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();

        const startTime = this.timeStringToMinutes(preferences['quiet-start']);
        const endTime = this.timeStringToMinutes(preferences['quiet-end']);

        // Handle overnight quiet hours (e.g., 22:00 to 08:00)
        if (startTime > endTime) {
            return currentTime >= startTime || currentTime <= endTime;
        } else {
            return currentTime >= startTime && currentTime <= endTime;
        }
    },

    // Convert time string to minutes since midnight
    timeStringToMinutes(timeString) {
        const [hours, minutes] = timeString.split(':').map(Number);
        return hours * 60 + minutes;
    },

    // Check if notification is urgent (should bypass quiet hours)
    isUrgentNotification(type) {
        const urgentTypes = ['order-placed', 'order-delivery'];
        return urgentTypes.includes(type);
    },

    // Get notification frequency setting
    getNotificationFrequency(type) {
        const preferences = this.getPreferences();

        if (type.includes('marketing') || type.includes('newsletter') || type.includes('promotional')) {
            return preferences['marketing-frequency'];
        }

        return preferences['email-frequency'];
    },

    // Export preferences for backup
    exportPreferences() {
        const preferences = this.getPreferences();
        return {
            preferences: preferences,
            exportedAt: new Date().toISOString(),
            version: '1.0'
        };
    },

    // Import preferences from backup
    importPreferences(importData) {
        try {
            if (importData.preferences) {
                const result = this.savePreferences(importData.preferences);
                if (result.success) {
                    this.applyPreferencesToUI(importData.preferences);
                    this.showPreferenceStatus('Preferences imported successfully', 'success');
                    return true;
                }
            }
            return false;
        } catch (error) {
            console.error('Error importing preferences:', error);
            this.showPreferenceStatus('Failed to import preferences', 'error');
            return false;
        }
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Utils;
}
