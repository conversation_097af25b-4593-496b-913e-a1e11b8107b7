/**
 * Magic Menu - Admin Dashboard Enhancements
 * Handles real-time updates, auto-refresh, and dashboard functionality
 */

const AdminDashboard = {
    // Configuration
    config: {
        autoRefreshInterval: 30000, // 30 seconds
        updateAnimationDuration: 300
    },

    // State
    autoRefreshEnabled: false,
    autoRefreshTimer: null,
    lastUpdateTime: null,

    // Initialize dashboard
    init() {
        this.setupDashboardControls();
        this.setupAutoRefresh();
        this.updateLastRefreshTime();
        this.startRealTimeUpdates();
        console.log('Admin Dashboard initialized');
    },

    // Setup dashboard controls
    setupDashboardControls() {
        const dashboardSection = document.getElementById('dashboard-section');
        if (!dashboardSection) return;

        // Add dashboard refresh controls
        const dashboardHeader = dashboardSection.querySelector('h2');
        if (dashboardHeader && !dashboardSection.querySelector('.dashboard-refresh')) {
            const refreshControls = document.createElement('div');
            refreshControls.className = 'dashboard-refresh';
            refreshControls.innerHTML = `
                <div class="dashboard-refresh-left">
                    <button type="button" class="btn btn-sm btn-secondary" onclick="AdminDashboard.refreshDashboard()">
                        <span class="refresh-icon">↻</span> Refresh
                    </button>
                    <span class="last-updated" id="last-updated">Last updated: Never</span>
                </div>
                <div class="dashboard-refresh-right">
                    <div class="auto-refresh-toggle">
                        <label class="checkbox-label">
                            <input type="checkbox" id="auto-refresh-toggle" onchange="AdminDashboard.toggleAutoRefresh()">
                            <span class="checkmark"></span>
                            Auto-refresh (30s)
                        </label>
                    </div>
                </div>
            `;
            dashboardHeader.after(refreshControls);
        }
    },

    // Setup auto-refresh functionality
    setupAutoRefresh() {
        // Load saved preference
        const savedPreference = localStorage.getItem('adminAutoRefresh');
        if (savedPreference === 'true') {
            const checkbox = document.getElementById('auto-refresh-toggle');
            if (checkbox) {
                checkbox.checked = true;
                this.enableAutoRefresh();
            }
        }
    },

    // Toggle auto-refresh
    toggleAutoRefresh() {
        const checkbox = document.getElementById('auto-refresh-toggle');
        if (checkbox.checked) {
            this.enableAutoRefresh();
        } else {
            this.disableAutoRefresh();
        }
        
        // Save preference
        localStorage.setItem('adminAutoRefresh', checkbox.checked.toString());
    },

    // Enable auto-refresh
    enableAutoRefresh() {
        this.autoRefreshEnabled = true;
        this.autoRefreshTimer = setInterval(() => {
            this.refreshDashboard(true);
        }, this.config.autoRefreshInterval);
        
        this.showUpdateIndicator('Auto-refresh enabled');
    },

    // Disable auto-refresh
    disableAutoRefresh() {
        this.autoRefreshEnabled = false;
        if (this.autoRefreshTimer) {
            clearInterval(this.autoRefreshTimer);
            this.autoRefreshTimer = null;
        }
        
        this.showUpdateIndicator('Auto-refresh disabled');
    },

    // Refresh dashboard
    async refreshDashboard(isAutoRefresh = false) {
        try {
            // Show loading state
            const refreshBtn = document.querySelector('.dashboard-refresh .btn');
            if (refreshBtn && !isAutoRefresh) {
                refreshBtn.classList.add('loading');
                refreshBtn.disabled = true;
            }

            // Simulate API delay
            await this.simulateAPICall(500);

            // Update all dashboard components
            this.updateDashboardStats();
            this.updateRecentOrders();
            this.updateQuickStats();
            this.updateLastRefreshTime();

            // Show success indicator
            if (!isAutoRefresh) {
                this.showUpdateIndicator('Dashboard refreshed');
            }

        } catch (error) {
            console.error('Error refreshing dashboard:', error);
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Failed to refresh dashboard', 'error');
            }
        } finally {
            // Reset loading state
            const refreshBtn = document.querySelector('.dashboard-refresh .btn');
            if (refreshBtn) {
                refreshBtn.classList.remove('loading');
                refreshBtn.disabled = false;
            }
        }
    },

    // Update dashboard stats
    updateDashboardStats() {
        // Get current data
        const orders = AdminOrderManager ? AdminOrderManager.orders : [];
        const today = new Date().toDateString();
        
        const todayOrders = orders.filter(order => 
            new Date(order.createdAt).toDateString() === today
        );
        
        const pendingOrders = orders.filter(order => 
            ['placed', 'confirmed', 'preparing', 'ready', 'out-for-delivery'].includes(order.status)
        );
        
        const todayRevenue = todayOrders.reduce((sum, order) => 
            sum + (order.totals?.total || 0), 0
        );

        const totalCustomers = this.getTotalCustomers();

        // Update stat cards with animation
        this.updateStatCard('Total Orders Today', todayOrders.length);
        this.updateStatCard('Revenue Today', `₦${todayRevenue.toLocaleString()}`);
        this.updateStatCard('Pending Orders', pendingOrders.length);
        this.updateStatCard('Total Customers', totalCustomers);
    },

    // Update stat card with animation
    updateStatCard(label, newValue) {
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach(card => {
            const labelElement = card.querySelector('.stat-label');
            if (labelElement && labelElement.textContent === label) {
                const numberElement = card.querySelector('.stat-number');
                if (numberElement) {
                    const oldValue = numberElement.textContent;
                    if (oldValue !== newValue.toString()) {
                        // Animate the change
                        numberElement.style.transform = 'scale(1.1)';
                        numberElement.style.color = 'var(--primary-color)';
                        
                        setTimeout(() => {
                            numberElement.textContent = newValue;
                            numberElement.style.transform = 'scale(1)';
                            numberElement.style.color = '';
                        }, this.config.updateAnimationDuration / 2);
                    }
                }
            }
        });
    },

    // Update recent orders
    updateRecentOrders() {
        const recentOrdersTable = document.querySelector('.recent-orders .data-table tbody');
        if (!recentOrdersTable || !AdminOrderManager) return;

        const recentOrders = AdminOrderManager.orders.slice(0, 5);
        
        if (recentOrders.length === 0) {
            recentOrdersTable.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">No recent orders</td>
                </tr>
            `;
            return;
        }

        recentOrdersTable.innerHTML = recentOrders.map(order => `
            <tr>
                <td><strong>${order.orderId}</strong></td>
                <td>${AdminOrderManager.escapeHtml(order.customerInfo?.name || 'Unknown')}</td>
                <td>${AdminOrderManager.formatOrderItems(order.items)}</td>
                <td>₦${order.totals?.total?.toLocaleString() || '0'}</td>
                <td><span class="badge badge-${AdminOrderManager.getStatusBadgeClass(order.status)}">${order.status}</span></td>
                <td>
                    <div class="action-buttons">
                        <button type="button" class="btn btn-sm btn-primary" onclick="AdminOrderManager.viewOrderDetails('${order.orderId}')">View</button>
                        ${order.status !== 'delivered' && order.status !== 'cancelled' ? 
                            `<button type="button" class="btn btn-sm btn-success" onclick="AdminOrderManager.quickStatusUpdate('${order.orderId}')">Update</button>` : 
                            ''
                        }
                    </div>
                </td>
            </tr>
        `).join('');
    },

    // Update quick stats
    updateQuickStats() {
        if (AdminMenuManager) {
            AdminMenuManager.updateDashboardStats();
        }
    },

    // Get total customers (simplified)
    getTotalCustomers() {
        const orders = AdminOrderManager ? AdminOrderManager.orders : [];
        const uniqueCustomers = new Set();
        
        orders.forEach(order => {
            if (order.customerInfo?.email) {
                uniqueCustomers.add(order.customerInfo.email);
            }
        });
        
        return uniqueCustomers.size;
    },

    // Update last refresh time
    updateLastRefreshTime() {
        this.lastUpdateTime = new Date();
        const lastUpdatedElement = document.getElementById('last-updated');
        if (lastUpdatedElement) {
            lastUpdatedElement.textContent = `Last updated: ${this.lastUpdateTime.toLocaleTimeString()}`;
        }
    },

    // Show update indicator
    showUpdateIndicator(message) {
        // Remove existing indicator
        const existingIndicator = document.querySelector('.update-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // Create new indicator
        const indicator = document.createElement('div');
        indicator.className = 'update-indicator';
        indicator.textContent = message;
        document.body.appendChild(indicator);

        // Show indicator
        setTimeout(() => {
            indicator.classList.add('show');
        }, 100);

        // Hide indicator after 3 seconds
        setTimeout(() => {
            indicator.classList.remove('show');
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.remove();
                }
            }, 300);
        }, 3000);
    },

    // Start real-time updates
    startRealTimeUpdates() {
        // Listen for storage changes (new orders, etc.)
        window.addEventListener('storage', (e) => {
            if (e.key && e.key.startsWith('orderTracking_')) {
                // New order or order update
                this.handleOrderUpdate();
            }
        });

        // Listen for visibility change to refresh when tab becomes active
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.lastUpdateTime) {
                const timeSinceUpdate = Date.now() - this.lastUpdateTime.getTime();
                // Refresh if more than 2 minutes since last update
                if (timeSinceUpdate > 120000) {
                    this.refreshDashboard(true);
                }
            }
        });
    },

    // Handle order update
    handleOrderUpdate() {
        // Reload order data
        if (AdminOrderManager) {
            AdminOrderManager.loadOrderData();
        }

        // Update dashboard if visible
        const dashboardSection = document.getElementById('dashboard-section');
        if (dashboardSection && dashboardSection.classList.contains('active')) {
            this.updateDashboardStats();
            this.updateRecentOrders();
            this.showUpdateIndicator('New order activity detected');
        }
    },

    // Cleanup on page unload
    cleanup() {
        if (this.autoRefreshTimer) {
            clearInterval(this.autoRefreshTimer);
        }
    },

    // Simulate API call
    async simulateAPICall(delay = 1000) {
        return new Promise(resolve => setTimeout(resolve, delay));
    }
};

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    AdminDashboard.cleanup();
});
