/**
 * Magic Menu - Account Management
 * Handles user authentication, registration, and account dashboard functionality
 */

const AccountManager = {
    // Initialize account functionality
    init() {
        this.setupEventListeners();
        this.checkAuthenticationStatus();
        this.initializePasswordToggles();
        console.log('Account Manager initialized');
    },

    // Set up event listeners
    setupEventListeners() {
        // Tab switching
        const authTabs = document.querySelectorAll('.auth-tab');
        authTabs.forEach(tab => {
            tab.addEventListener('click', (e) => this.switchAuthTab(e.target.dataset.tab));
        });

        // Form submissions
        const loginForm = document.getElementById('login-form-element');
        const registerForm = document.getElementById('register-form-element');
        
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }
        
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }

        // Logout button
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.handleLogout());
        }

        // Edit profile button
        const editProfileBtn = document.getElementById('edit-profile-btn');
        if (editProfileBtn) {
            editProfileBtn.addEventListener('click', () => this.editProfile());
        }

        // Forgot password link
        const forgotPasswordLink = document.querySelector('.forgot-password-link');
        if (forgotPasswordLink) {
            forgotPasswordLink.addEventListener('click', (e) => this.handleForgotPassword(e));
        }
    },

    // Switch between login and register tabs
    switchAuthTab(tabName) {
        // Update tab buttons
        const tabs = document.querySelectorAll('.auth-tab');
        tabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });

        // Update form visibility
        const forms = document.querySelectorAll('.auth-form');
        forms.forEach(form => {
            form.classList.toggle('active', form.id === `${tabName}-form`);
        });

        // Clear any existing form errors
        this.clearFormErrors();
    },

    // Check if user is already authenticated
    checkAuthenticationStatus() {
        const user = Utils.storage.get('user');
        
        if (user && user.loggedIn) {
            this.showUserDashboard(user);
        } else {
            this.showAuthForms();
        }
    },

    // Show authentication forms
    showAuthForms() {
        const authContainer = document.getElementById('auth-container');
        const userDashboard = document.getElementById('user-dashboard');
        
        if (authContainer) authContainer.classList.remove('hidden');
        if (userDashboard) userDashboard.classList.add('hidden');
    },

    // Show user dashboard
    showUserDashboard(user) {
        const authContainer = document.getElementById('auth-container');
        const userDashboard = document.getElementById('user-dashboard');
        
        if (authContainer) authContainer.classList.add('hidden');
        if (userDashboard) userDashboard.classList.remove('hidden');

        // Update user information
        this.updateUserInfo(user);
        this.loadRecentOrders();
    },

    // Update user information in dashboard
    updateUserInfo(user) {
        const elements = {
            'user-name': user.name || user.email.split('@')[0],
            'user-email': user.email,
            'profile-name': user.name || `${user.firstName || ''} ${user.lastName || ''}`.trim(),
            'profile-email': user.email,
            'profile-phone': user.phone || 'Not provided',
            'profile-member-since': user.registrationTime ? 
                new Date(user.registrationTime).toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long' 
                }) : 'Recently'
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) element.textContent = value;
        });
    },

    // Handle login form submission
    async handleLogin(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        // Clear previous errors
        this.clearFormErrors();

        // Validate form
        if (!this.validateLoginForm(data)) {
            return;
        }

        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Signing In...';
        submitBtn.disabled = true;

        try {
            // Simulate API call
            await this.simulateAPICall(1500);

            // For demo purposes, accept any valid email/password combination
            if (data.email && data.password) {
                const user = {
                    email: data.email,
                    name: data.email.split('@')[0],
                    loggedIn: true,
                    loginTime: new Date().toISOString()
                };

                Utils.storage.set('user', user);
                
                if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                    MagicMenu.showToast('Welcome back! Login successful.', 'success');
                }

                // Show dashboard
                this.showUserDashboard(user);
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showFormError('login-email', 'Login failed. Please try again.');
        } finally {
            // Reset button state
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    },

    // Handle registration form submission
    async handleRegister(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        // Clear previous errors
        this.clearFormErrors();

        // Validate form
        if (!this.validateRegisterForm(data)) {
            return;
        }

        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Creating Account...';
        submitBtn.disabled = true;

        try {
            // Simulate API call
            await this.simulateAPICall(2000);

            // Create user account
            const user = {
                email: data.email,
                name: `${data.firstName} ${data.lastName}`,
                firstName: data.firstName,
                lastName: data.lastName,
                phone: data.phone,
                loggedIn: true,
                registrationTime: new Date().toISOString(),
                newsletter: data.newsletter === 'on'
            };

            Utils.storage.set('user', user);
            
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Account created successfully! Welcome to Magic Menu.', 'success');
            }

            // Show dashboard
            this.showUserDashboard(user);
        } catch (error) {
            console.error('Registration error:', error);
            this.showFormError('register-email', 'Registration failed. Please try again.');
        } finally {
            // Reset button state
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    },

    // Handle logout
    handleLogout() {
        Utils.storage.remove('user');
        
        if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
            MagicMenu.showToast('You have been signed out successfully.', 'info');
        }

        this.showAuthForms();
        
        // Reset forms
        const forms = document.querySelectorAll('.form');
        forms.forEach(form => form.reset());
        
        // Switch to login tab
        this.switchAuthTab('login');
    },

    // Handle forgot password
    handleForgotPassword(e) {
        e.preventDefault();
        
        const email = document.getElementById('login-email').value;
        
        if (!email) {
            this.showFormError('login-email', 'Please enter your email address first.');
            return;
        }

        if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
            MagicMenu.showToast('Password reset instructions have been sent to your email.', 'info');
        }
    },

    // Edit profile (placeholder)
    editProfile() {
        if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
            MagicMenu.showToast('Profile editing feature coming soon!', 'info');
        }
    },

    // Load recent orders
    loadRecentOrders() {
        const recentOrdersContainer = document.getElementById('recent-orders');
        if (!recentOrdersContainer) return;

        const lastOrder = Utils.storage.get('lastOrder');
        
        if (lastOrder) {
            recentOrdersContainer.innerHTML = `
                <div class="order-item">
                    <div class="order-info">
                        <div class="order-id">Order #${lastOrder.id || 'N/A'}</div>
                        <div class="order-date">${new Date(lastOrder.orderTime).toLocaleDateString()}</div>
                        <div class="order-total">${Utils.formatCurrency(lastOrder.totals?.total || 0)}</div>
                    </div>
                    <div class="order-status">
                        <span class="status-badge status-completed">Completed</span>
                    </div>
                </div>
            `;
        } else {
            recentOrdersContainer.innerHTML = `
                <p class="no-orders">No recent orders found. <a href="menu.html">Start ordering now!</a></p>
            `;
        }
    },

    // Initialize password toggle functionality
    initializePasswordToggles() {
        const passwordToggles = document.querySelectorAll('.password-toggle');
        
        passwordToggles.forEach(toggle => {
            toggle.addEventListener('click', () => {
                const passwordInput = toggle.parentElement.querySelector('input[type="password"], input[type="text"]');
                const icon = toggle.querySelector('.password-toggle-icon');
                
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.textContent = 'Hide';
                    toggle.setAttribute('aria-label', 'Hide password');
                } else {
                    passwordInput.type = 'password';
                    icon.textContent = 'Show';
                    toggle.setAttribute('aria-label', 'Show password');
                }
            });
        });
    },

    // Validate login form
    validateLoginForm(data) {
        let isValid = true;

        if (!data.email) {
            this.showFormError('login-email', 'Email address is required.');
            isValid = false;
        } else if (!Utils.isValidEmail(data.email)) {
            this.showFormError('login-email', 'Please enter a valid email address.');
            isValid = false;
        }

        if (!data.password) {
            this.showFormError('login-password', 'Password is required.');
            isValid = false;
        }

        return isValid;
    },

    // Validate registration form
    validateRegisterForm(data) {
        let isValid = true;

        if (!data.firstName) {
            this.showFormError('register-firstName', 'First name is required.');
            isValid = false;
        }

        if (!data.lastName) {
            this.showFormError('register-lastName', 'Last name is required.');
            isValid = false;
        }

        if (!data.email) {
            this.showFormError('register-email', 'Email address is required.');
            isValid = false;
        } else if (!Utils.isValidEmail(data.email)) {
            this.showFormError('register-email', 'Please enter a valid email address.');
            isValid = false;
        }

        if (!data.phone) {
            this.showFormError('register-phone', 'Phone number is required.');
            isValid = false;
        }

        if (!data.password) {
            this.showFormError('register-password', 'Password is required.');
            isValid = false;
        } else if (data.password.length < 8) {
            this.showFormError('register-password', 'Password must be at least 8 characters long.');
            isValid = false;
        }

        if (!data.confirmPassword) {
            this.showFormError('register-confirmPassword', 'Please confirm your password.');
            isValid = false;
        } else if (data.password !== data.confirmPassword) {
            this.showFormError('register-confirmPassword', 'Passwords do not match.');
            isValid = false;
        }

        if (!data.terms) {
            this.showFormError('register-terms', 'You must agree to the Terms of Service and Privacy Policy.');
            isValid = false;
        }

        return isValid;
    },

    // Show form error
    showFormError(fieldId, message) {
        const errorElement = document.getElementById(`${fieldId}-error`);
        const inputElement = document.getElementById(fieldId);
        
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
        
        if (inputElement) {
            inputElement.classList.add('error');
        }
    },

    // Clear all form errors
    clearFormErrors() {
        const errorElements = document.querySelectorAll('.form-error');
        const inputElements = document.querySelectorAll('.form-control.error');
        
        errorElements.forEach(element => {
            element.textContent = '';
            element.style.display = 'none';
        });
        
        inputElements.forEach(element => {
            element.classList.remove('error');
        });
    },

    // Simulate API call
    simulateAPICall(delay = 1000) {
        return new Promise(resolve => setTimeout(resolve, delay));
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    AccountManager.init();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AccountManager;
}
