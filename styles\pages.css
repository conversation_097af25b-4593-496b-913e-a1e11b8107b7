/* Magic Menu - Page-Specific Styles */

/* Homepage Styles */
.hero {
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
    color: var(--white);
    padding: var(--spacing-3xl) 0;
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.hero-text h1 {
    color: var(--white);
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-lg);
    line-height: var(--line-height-tight);
}

.hero-description {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-xl);
    opacity: 0.95;
}

.hero-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
}

/* Section Headers */
.section-header {
    margin-bottom: var(--spacing-2xl);
}

.section-header h2 {
    margin-bottom: var(--spacing-md);
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

/* Popular Dishes */
.dishes-grid {
    margin-bottom: var(--spacing-2xl);
}

.dish-card {
    transition: all var(--transition-normal);
}

.dish-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.dish-price {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

/* Features Section */
.features {
    background-color: var(--white);
}

.feature-card {
    text-align: center;
    border: none;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.feature-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

/* Email Verification Page */
.verification-section {
    min-height: 60vh;
    display: flex;
    align-items: center;
    padding: var(--spacing-3xl) 0;
}

.verification-container {
    max-width: 500px;
    margin: 0 auto;
    text-align: center;
}

.verification-state {
    padding: var(--spacing-2xl);
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.verification-state.hidden {
    display: none;
}

.verification-icon {
    margin-bottom: var(--spacing-xl);
    display: flex;
    justify-content: center;
    align-items: center;
}

.verification-icon svg {
    width: 64px;
    height: 64px;
}

.loading-spinner {
    width: 64px;
    height: 64px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.verification-state h2 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-dark);
}

.verification-state p {
    margin-bottom: var(--spacing-xl);
    color: var(--text-light);
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
}

.verification-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: center;
}

.verification-actions .btn {
    min-width: 200px;
}

/* Success state specific styles */
.verification-state .verification-icon.success svg circle {
    fill: var(--success-color);
}

/* Error state specific styles */
.verification-state .verification-icon.error svg circle {
    fill: var(--error-color);
}

/* Info state specific styles */
.verification-state .verification-icon.info svg circle {
    fill: var(--info-color);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
}

/* Testimonials */
.testimonials {
    background-color: var(--background-color);
}

.testimonial-card {
    border-left: 4px solid var(--primary-color);
}

.testimonial-rating {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
}

.testimonial-text {
    font-style: italic;
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-lg);
    color: var(--text-color);
}

.testimonial-author {
    font-weight: var(--font-weight-semibold);
    color: var(--primary-color);
}

/* Call to Action */
.cta {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
}

.cta-content h2 {
    color: var(--white);
    margin-bottom: var(--spacing-lg);
}

.cta-content p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xl);
    opacity: 0.95;
}

.cta-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: center;
}

/* Footer */
.footer {
    background-color: var(--secondary-color);
    color: var(--white);
    padding: var(--spacing-3xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-section h3,
.footer-section h4 {
    color: var(--white);
    margin-bottom: var(--spacing-lg);
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section a {
    color: rgba(255, 255, 255, 0.8);
    transition: color var(--transition-fast);
}

.footer-section a:hover {
    color: var(--white);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.social-links a {
    font-size: var(--font-size-2xl);
    text-decoration: none;
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
}

/* Menu Page Styles */
.menu-header {
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-2xl) 0;
    text-align: center;
}

.menu-header h1 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.menu-filters {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    justify-content: center;
    margin-bottom: var(--spacing-2xl);
}

.filter-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: transparent;
    color: var(--text-color);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-xl);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-weight: var(--font-weight-semibold);
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.menu-category {
    margin-bottom: var(--spacing-3xl);
}

.category-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.category-title {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-sm);
}

.category-description {
    color: var(--text-light);
    font-size: var(--font-size-lg);
}

.menu-items {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
}

.menu-item {
    display: flex;
    gap: var(--spacing-lg);
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--transition-normal);
    position: relative;
}

.menu-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.menu-item-image {
    flex-shrink: 0;
    width: 150px;
    height: 150px;
}

.menu-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.menu-item-content {
    flex: 1;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.menu-item-header {
    margin-bottom: var(--spacing-md);
}

.menu-item-title {
    margin-bottom: var(--spacing-xs);
    color: var(--secondary-color);
}

.menu-item-meta {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

.menu-item-category {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    font-weight: var(--font-weight-semibold);
}

.spice-level {
    font-size: var(--font-size-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-weight: var(--font-weight-semibold);
}

.spice-level.mild {
    background-color: var(--success-color);
    color: var(--white);
}

.spice-level.medium {
    background-color: var(--warning-color);
    color: var(--text-color);
}

.spice-level.hot {
    background-color: var(--error-color);
    color: var(--white);
}

.menu-item-description {
    color: var(--text-light);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-md);
}

.menu-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.menu-item-price {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
}

.popular-badge {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background-color: var(--accent-color);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
}

/* Cart Page Styles */
.cart-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
}

.cart-items {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
}

.cart-item {
    display: grid;
    grid-template-columns: 80px 1fr auto auto auto;
    gap: var(--spacing-md);
    align-items: center;
    padding: var(--spacing-lg) 0;
    border-bottom: 1px solid var(--border-color);
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-image img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius-md);
}

.cart-item-name {
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-xs);
}

.cart-item-category {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.cart-item-price {
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

.cart-item-quantity {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.quantity-decrease,
.quantity-increase {
    width: 32px;
    height: 32px;
    border: 1px solid var(--border-color);
    background-color: var(--white);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    transition: all var(--transition-fast);
}

.quantity-decrease:hover,
.quantity-increase:hover {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.quantity-input {
    width: 60px;
    text-align: center;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs);
}

.cart-item-total {
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
}

.cart-summary {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    height: fit-content;
}

.summary-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.summary-line.total {
    border-top: 2px solid var(--border-color);
    padding-top: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.cart-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.empty-cart {
    text-align: center;
    padding: var(--spacing-3xl);
    color: var(--text-light);
}

.empty-cart h3 {
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
}

/* Page Headers */
.page-header {
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-2xl) 0;
    text-align: center;
}

.page-header h1 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.page-header p {
    font-size: var(--font-size-lg);
    opacity: 0.9;
}

/* Form Styles */
.form-container {
    max-width: 600px;
    margin: 0 auto;
    background-color: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.form-section {
    margin-bottom: var(--spacing-2xl);
}

.form-section:last-child {
    margin-bottom: 0;
}

.form-section-title {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

/* Checkout Styles */
.checkout-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
}

.checkout-form {
    background-color: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.order-summary {
    background-color: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    height: fit-content;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-color);
}

.order-item:last-child {
    border-bottom: none;
}

.order-item-details h4 {
    margin-bottom: var(--spacing-xs);
}

.order-item-meta {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.order-item-price {
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
}

/* Contact Page Styles */
.contact-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-3xl);
}

.contact-info h2 {
    margin-bottom: var(--spacing-lg);
}

.contact-methods {
    margin: var(--spacing-2xl) 0;
}

.contact-method {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-lg);
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.contact-icon {
    font-size: var(--font-size-2xl);
    flex-shrink: 0;
}

.contact-details h3 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-sm);
}

.contact-details p,
.contact-details address {
    color: var(--text-light);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-xs);
}

.contact-details address {
    font-style: normal;
}

.contact-details a {
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
}

.social-contact {
    margin-top: var(--spacing-2xl);
    padding: var(--spacing-lg);
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.social-contact h3 {
    margin-bottom: var(--spacing-md);
}

.social-contact .social-links {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.social-contact .social-links a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    transition: background-color var(--transition-fast);
}

.social-contact .social-links a:hover {
    background-color: var(--background-color);
}

.contact-form-container {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
    margin-top: 2px;
}

.contact-faq .faq-item {
    background-color: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.contact-faq .faq-item h3 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
}

.contact-faq .faq-item p {
    color: var(--text-light);
    line-height: var(--line-height-relaxed);
}

/* About Page Styles */
.our-story,
.our-mission,
.meet-team,
.our-commitment {
    padding: var(--spacing-3xl) 0;
}

.story-content p {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-lg);
    color: var(--text-light);
}

.story-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.mission-statement {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-relaxed);
    color: var(--text-light);
    max-width: 800px;
    margin: 0 auto var(--spacing-3xl);
    font-style: italic;
}

.value-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
}

.team-member {
    text-align: center;
}

.team-role {
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-md);
}

.commitment-grid {
    margin-top: var(--spacing-2xl);
}

.commitment-item {
    text-align: center;
    padding: var(--spacing-lg);
}

.commitment-item h3 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.commitment-item p {
    color: var(--text-light);
    line-height: var(--line-height-relaxed);
}

/* Checkout Page Styles */
.checkout-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
}

.checkout-form {
    background-color: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.payment-methods {
    display: grid;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.payment-method {
    display: block;
    cursor: pointer;
}

.payment-method input[type="radio"] {
    display: none;
}

.payment-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-fast);
}

.payment-method input[type="radio"]:checked + .payment-option {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.payment-icon {
    font-size: var(--font-size-xl);
}

.payment-text {
    font-weight: var(--font-weight-semibold);
}

.card-details {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background-color: var(--background-color);
    border-radius: var(--border-radius-md);
}

.order-summary {
    background-color: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    height: fit-content;
    position: sticky;
    top: var(--spacing-lg);
}

.order-summary h2 {
    margin-bottom: var(--spacing-lg);
    color: var(--secondary-color);
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-color);
}

.order-item:last-child {
    border-bottom: none;
}

.order-item-details h4 {
    margin-bottom: var(--spacing-xs);
    color: var(--text-color);
}

.order-item-meta {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.order-item-price {
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
}

.order-totals {
    margin: var(--spacing-lg) 0;
    padding-top: var(--spacing-lg);
    border-top: 2px solid var(--border-color);
}

.delivery-info {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background-color: var(--background-color);
    border-radius: var(--border-radius-md);
    text-align: center;
}

.delivery-info h3 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-sm);
}

.delivery-info p {
    margin-bottom: var(--spacing-xs);
    color: var(--text-light);
}

/* Confirmation Page Styles */
.confirmation-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.confirmation-icon {
    font-size: 4rem;
    color: var(--success-color);
    margin-bottom: var(--spacing-lg);
}

.order-number {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.confirmation-details {
    background-color: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    margin: var(--spacing-2xl) 0;
    text-align: left;
}

.confirmation-section {
    margin-bottom: var(--spacing-lg);
}

.confirmation-section:last-child {
    margin-bottom: 0;
}

.confirmation-section h3 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.confirmation-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-top: var(--spacing-2xl);
}

/* Account Page Styles */
.account-section {
    padding: var(--spacing-2xl) 0;
}

/* Authentication Container */
.auth-container {
    max-width: 500px;
    margin: 0 auto;
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.auth-tabs {
    display: flex;
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
}

.auth-tab {
    flex: 1;
    padding: var(--spacing-lg);
    background: none;
    border: none;
    font-family: var(--font-heading);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-light);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.auth-tab:hover {
    color: var(--primary-color);
}

.auth-tab.active {
    background-color: var(--white);
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

.auth-form {
    display: none;
    padding: var(--spacing-2xl);
}

.auth-form.active {
    display: block;
}

.auth-form h2 {
    text-align: center;
    margin-bottom: var(--spacing-sm);
    color: var(--secondary-color);
    font-family: var(--font-heading);
    font-weight: var(--font-weight-bold);
}

.auth-form p {
    text-align: center;
    color: var(--text-light);
    margin-bottom: var(--spacing-2xl);
}

.auth-footer {
    text-align: center;
    margin-top: var(--spacing-lg);
}

.auth-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-weight-semibold);
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* User Dashboard */
.user-dashboard {
    max-width: 1000px;
    margin: 0 auto;
}

.user-dashboard.hidden {
    display: none;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-xl);
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.user-info h2 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--secondary-color);
    font-family: var(--font-heading);
}

.user-info p {
    margin: 0;
    color: var(--text-light);
}

.dashboard-content {
    margin-top: var(--spacing-xl);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

.dashboard-card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
    margin: 0;
    color: var(--secondary-color);
    font-family: var(--font-heading);
    font-weight: var(--font-weight-semibold);
}

.card-content {
    padding: var(--spacing-lg);
}

/* Profile Information */
.profile-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
}

.info-value {
    color: var(--text-light);
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-lg);
    background-color: var(--background-color);
    border-radius: var(--border-radius-md);
    text-decoration: none;
    color: var(--text-color);
    transition: all var(--transition-fast);
}

.action-btn:hover {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    transform: translateY(-2px);
}

.action-icon {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-sm);
}

.action-text {
    font-weight: var(--font-weight-semibold);
    text-align: center;
}

/* Recent Orders */
.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background-color: var(--background-color);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-sm);
}

.order-item:last-child {
    margin-bottom: 0;
}

.order-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.order-id {
    font-weight: var(--font-weight-semibold);
    color: var(--secondary-color);
}

.order-date {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.order-total {
    font-weight: var(--font-weight-semibold);
    color: var(--primary-color);
}

.order-status {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
}

.status-completed {
    background-color: var(--success-color);
    color: var(--white);
}

.no-orders {
    text-align: center;
    color: var(--text-light);
    font-style: italic;
}

.no-orders a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-weight-semibold);
}

.no-orders a:hover {
    text-decoration: underline;
}

.profile-info {
    display: grid;
    gap: var(--spacing-lg);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background-color: var(--background-color);
    border-radius: var(--border-radius-md);
}

.info-label {
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
}

.info-value {
    color: var(--text-light);
}

.order-history-item {
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.order-id {
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
}

.order-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
}

.order-status.delivered {
    background-color: var(--success-color);
    color: var(--white);
}

.order-status.pending {
    background-color: var(--warning-color);
    color: var(--text-color);
}

.order-status.cancelled {
    background-color: var(--error-color);
    color: var(--white);
}

/* Auth Forms Styles */
.auth-forms {
    max-width: 500px;
    margin: 0 auto;
}

.auth-container {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.auth-tabs {
    display: flex;
    background-color: var(--background-color);
}

.auth-tab {
    flex: 1;
    padding: var(--spacing-lg);
    background: none;
    border: none;
    cursor: pointer;
    font-weight: var(--font-weight-semibold);
    color: var(--text-light);
    transition: all var(--transition-fast);
}

.auth-tab.active {
    background-color: var(--white);
    color: var(--primary-color);
}

.auth-form {
    display: none;
    padding: var(--spacing-2xl);
}

.auth-form.active {
    display: block;
}

.auth-form h2 {
    text-align: center;
    margin-bottom: var(--spacing-sm);
    color: var(--secondary-color);
}

.auth-form p {
    text-align: center;
    color: var(--text-light);
    margin-bottom: var(--spacing-2xl);
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    font-size: var(--font-size-lg);
    padding: var(--spacing-xs);
}

.auth-footer {
    text-align: center;
    margin-top: var(--spacing-lg);
}

.forgot-password {
    color: var(--primary-color);
    font-size: var(--font-size-sm);
}

.user-info {
    text-align: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-lg);
}

.user-info h3 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
}

.user-info p {
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

.preferences-form {
    max-width: 400px;
}

.empty-state {
    text-align: center;
    padding: var(--spacing-3xl);
    color: var(--text-light);
}

.empty-state p {
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-lg);
}

.order-details {
    margin: var(--spacing-md) 0;
}

.order-details p {
    margin-bottom: var(--spacing-xs);
    color: var(--text-light);
}

.order-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

/* Admin Page Styles */
.admin-header {
    background-color: var(--secondary-color);
    color: var(--white);
    padding: var(--spacing-2xl) 0;
    text-align: center;
}

.admin-header h1 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.admin-nav {
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-2xl);
}

.admin-nav-container {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg) 0;
}

.admin-nav-link {
    padding: var(--spacing-sm) var(--spacing-lg);
    color: var(--text-color);
    text-decoration: none;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-fast);
    font-weight: var(--font-weight-semibold);
}

.admin-nav-link:hover,
.admin-nav-link.active {
    background-color: var(--primary-color);
    color: var(--white);
}

.admin-section {
    display: none;
    background-color: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.admin-section.active {
    display: block;
}

.admin-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background-color: var(--background-color);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    color: var(--text-light);
    font-weight: var(--font-weight-semibold);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--spacing-lg);
}

.data-table th,
.data-table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background-color: var(--background-color);
    font-weight: var(--font-weight-semibold);
    color: var(--secondary-color);
}

.data-table tr:hover {
    background-color: var(--background-color);
}

.action-buttons {
    display: flex;
    gap: var(--spacing-sm);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

/* Admin Login Overlay */
.admin-login-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
}

.admin-login-container {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 400px;
    max-height: 90vh;
    overflow-y: auto;
}

.admin-login-form {
    padding: var(--spacing-2xl);
}

.admin-login-form h2 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-sm);
    text-align: center;
}

.admin-login-form p {
    color: var(--text-light);
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.admin-login-footer {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.admin-login-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-weight-semibold);
}

.admin-login-footer a:hover {
    text-decoration: underline;
}

.demo-credentials {
    background-color: var(--background-color);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-top: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    font-size: var(--font-size-sm);
    padding: var(--spacing-xs);
}

.password-toggle:hover {
    color: var(--primary-color);
}

.login-attempts,
.lockout-message {
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    text-align: center;
}

.login-attempts {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
}

.lockout-message {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
}

.text-warning {
    color: #856404;
}

.text-error {
    color: #721c24;
}

/* Admin Header Updates */
.admin-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.admin-title h1 {
    margin-bottom: var(--spacing-xs);
}

.admin-user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.admin-user-name {
    font-weight: var(--font-weight-semibold);
    color: var(--white);
}

.admin-user-role {
    background-color: rgba(255, 255, 255, 0.2);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    text-transform: capitalize;
    color: var(--white);
}

/* Admin Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    padding: var(--spacing-lg);
}

.modal-dialog {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-dialog.modal-sm {
    max-width: 400px;
}

.modal-dialog.modal-lg {
    max-width: 800px;
}

.modal-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.modal-header h3 {
    margin: 0;
    color: var(--secondary-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    cursor: pointer;
    color: var(--text-light);
    padding: var(--spacing-xs);
    line-height: 1;
}

.modal-close:hover {
    color: var(--text-color);
}

.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    flex: 1;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    flex-shrink: 0;
}

/* Form Styles in Modals */
.form-row {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-row .form-group {
    flex: 1;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.checkbox-group .checkbox-label {
    margin-bottom: 0;
}

/* Order Details Styles */
.order-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.order-info-section {
    background-color: var(--background-color);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
}

.order-info-section h4 {
    margin-bottom: var(--spacing-md);
    color: var(--secondary-color);
}

.info-grid {
    display: grid;
    gap: var(--spacing-sm);
}

.order-items-section,
.order-timeline-section {
    margin-bottom: var(--spacing-lg);
}

.order-items-section h4,
.order-timeline-section h4 {
    margin-bottom: var(--spacing-md);
    color: var(--secondary-color);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: var(--spacing-sm);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background-color: var(--background-color);
    font-weight: var(--font-weight-semibold);
}

.table tfoot td {
    border-top: 2px solid var(--border-color);
    font-weight: var(--font-weight-semibold);
}

/* Timeline Styles */
.timeline {
    position: relative;
    padding-left: var(--spacing-lg);
}

.timeline::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-lg);
    padding-left: var(--spacing-lg);
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 4px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--primary-color);
    border: 2px solid var(--white);
}

.timeline-time {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-bottom: var(--spacing-xs);
}

.timeline-content {
    color: var(--text-color);
}

/* Alert Styles */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
}

.alert-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

/* Badge Styles */
.badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--border-radius-sm);
    text-transform: uppercase;
}

.badge-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.badge-secondary {
    background-color: var(--text-light);
    color: var(--white);
}

.badge-success {
    background-color: #28a745;
    color: var(--white);
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-danger {
    background-color: #dc3545;
    color: var(--white);
}

.badge-info {
    background-color: #17a2b8;
    color: var(--white);
}

/* Section Controls */
.section-controls {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.section-controls-left {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.section-controls-right {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: flex-end;
}

.bulk-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
    padding: var(--spacing-sm);
    background-color: var(--background-color);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
    flex-wrap: wrap;
}

.bulk-actions.hidden {
    display: none;
}

.bulk-actions select {
    min-width: 150px;
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Checkbox Styles for Bulk Selection */
.bulk-checkbox {
    margin-right: var(--spacing-sm);
}

.bulk-select-all {
    margin-bottom: var(--spacing-md);
}

/* Enhanced Data Table */
.data-table-enhanced {
    position: relative;
}

.data-table-enhanced.bulk-mode .data-table th:first-child,
.data-table-enhanced.bulk-mode .data-table td:first-child {
    width: 40px;
    text-align: center;
}

/* Real-time Updates Indicator */
.update-indicator {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

.update-indicator.show {
    transform: translateX(0);
}

/* Dashboard Enhancements */
.dashboard-refresh {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.last-updated {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.auto-refresh-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

/* Quick Stats Cards */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.quick-stat-card {
    background-color: var(--white);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.quick-stat-card.warning {
    border-left-color: #ffc107;
}

.quick-stat-card.danger {
    border-left-color: #dc3545;
}

.quick-stat-card.success {
    border-left-color: #28a745;
}

.quick-stat-title {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin-bottom: var(--spacing-xs);
}

.quick-stat-value {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--secondary-color);
}

.quick-stat-change {
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-xs);
}

.quick-stat-change.positive {
    color: #28a745;
}

.quick-stat-change.negative {
    color: #dc3545;
}

/* FAQ Page Styles */
.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-category {
    margin-bottom: var(--spacing-3xl);
}

.faq-category-title {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
}

.faq-item {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-sm);
}

.faq-question {
    width: 100%;
    padding: var(--spacing-lg);
    background: none;
    border: none;
    text-align: left;
    cursor: pointer;
    font-weight: var(--font-weight-semibold);
    color: var(--secondary-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: color var(--transition-fast);
}

.faq-question:hover {
    color: var(--primary-color);
}

.faq-question::after {
    content: '+';
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    transition: transform var(--transition-fast);
}

.faq-question.active::after {
    transform: rotate(45deg);
}

.faq-answer {
    display: none;
    padding: 0 var(--spacing-lg) var(--spacing-lg);
    color: var(--text-light);
    line-height: var(--line-height-relaxed);
}

.faq-answer.active {
    display: block;
}

/* Privacy and Terms Pages */
.legal-content {
    max-width: 800px;
    margin: 0 auto;
    background-color: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.legal-content h2 {
    color: var(--secondary-color);
    margin-top: var(--spacing-2xl);
    margin-bottom: var(--spacing-lg);
}

.legal-content h3 {
    color: var(--primary-color);
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.legal-content p,
.legal-content li {
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-md);
    color: var(--text-light);
}

.legal-content ul,
.legal-content ol {
    margin-left: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.last-updated {
    font-style: italic;
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-2xl);
}

/* Address Management */
.address-list {
    display: grid;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.address-card {
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    position: relative;
}

.address-card.default {
    border-color: var(--primary-color);
    background-color: var(--primary-color-light);
}

.address-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.address-label {
    font-weight: var(--font-weight-semibold);
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
}

.address-default-badge {
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
}

.address-details {
    color: var(--text-color);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-md);
}

.address-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.address-actions .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.empty-addresses {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-light);
}

.empty-addresses p {
    margin-bottom: var(--spacing-lg);
}

/* Order Details */
.order-summary {
    display: grid;
    gap: var(--spacing-lg);
}

.order-info h4 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
}

.order-items h5,
.delivery-info h5 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid var(--border-color);
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-sm);
}

.item-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.item-name {
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
}

.item-quantity {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.item-price {
    font-weight: var(--font-weight-semibold);
    color: var(--primary-color);
}

.order-totals {
    background-color: var(--background-color);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
}

.total-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.total-line.total {
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-sm);
    margin-top: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.delivery-info p {
    margin-bottom: var(--spacing-xs);
}

/* Account Settings */
.settings-group {
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.settings-group:last-child {
    border-bottom: none;
}

.settings-group h3 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-lg);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
}

.setting-info h4 {
    margin-bottom: var(--spacing-xs);
    color: var(--text-color);
}

.setting-info p {
    color: var(--text-light);
    font-size: var(--font-size-sm);
    margin: 0;
}

.danger-zone {
    border-color: var(--error-color);
    background-color: rgba(220, 53, 69, 0.05);
}

.danger-zone .setting-item {
    border-color: var(--error-color);
    background-color: var(--white);
}

.warning-message {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.warning-message h4 {
    color: #856404;
    margin-bottom: var(--spacing-md);
}

.warning-message p {
    color: #856404;
    margin-bottom: var(--spacing-sm);
}

.warning-message ul {
    color: #856404;
    margin: var(--spacing-md) 0;
    padding-left: var(--spacing-lg);
}

.warning-message li {
    margin-bottom: var(--spacing-xs);
}
