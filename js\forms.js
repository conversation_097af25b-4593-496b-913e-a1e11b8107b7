/**
 * Magic Menu - Form Validation and Handling
 * Handles form validation, submission, and user feedback
 */

const Forms = {
    // Initialize form handling
    init() {
        this.setupEventListeners();
        this.initializeValidation();
    },

    // Set up event listeners for forms
    setupEventListeners() {
        // Real-time validation on input
        document.addEventListener('input', (e) => {
            if (e.target.matches('.form-control')) {
                this.validateField(e.target);
            }
        });

        // Form submission
        document.addEventListener('submit', (e) => {
            if (e.target.matches('form[data-validate]')) {
                e.preventDefault();
                this.handleFormSubmit(e.target);
            }
        });

        // Password visibility toggle
        document.addEventListener('click', (e) => {
            if (e.target.matches('.password-toggle')) {
                this.togglePasswordVisibility(e.target);
            }
        });
    },

    // Initialize validation for existing forms
    initializeValidation() {
        const forms = document.querySelectorAll('form[data-validate]');
        forms.forEach(form => {
            this.setupFormValidation(form);
        });
    },

    // Set up validation for a specific form
    setupFormValidation(form) {
        const fields = form.querySelectorAll('.form-control');
        fields.forEach(field => {
            // Add validation attributes if not present
            if (field.hasAttribute('required') && !field.hasAttribute('aria-required')) {
                field.setAttribute('aria-required', 'true');
            }
        });
    },

    // Validate individual field
    validateField(field) {
        const value = field.value.trim();
        const fieldType = field.type;
        const fieldName = field.name || field.id;
        let isValid = true;
        let errorMessage = '';

        // Clear previous validation state
        this.clearFieldValidation(field);

        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = `${this.getFieldLabel(field)} is required.`;
        }
        // Email validation
        else if (fieldType === 'email' && value && !Utils.isValidEmail(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid email address.';
        }
        // Phone validation
        else if (fieldType === 'tel' && value && !Utils.isValidPhone(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid Nigerian phone number.';
        }
        // Password validation
        else if (fieldType === 'password' && value && value.length < 8) {
            isValid = false;
            errorMessage = 'Password must be at least 8 characters long.';
        }
        // Confirm password validation
        else if (fieldName === 'confirmPassword' || fieldName === 'password_confirm') {
            const passwordField = field.form.querySelector('input[type="password"]:not([name*="confirm"])');
            if (passwordField && value !== passwordField.value) {
                isValid = false;
                errorMessage = 'Passwords do not match.';
            }
        }
        // Custom validation patterns
        else if (field.hasAttribute('pattern') && value) {
            const pattern = new RegExp(field.getAttribute('pattern'));
            if (!pattern.test(value)) {
                isValid = false;
                errorMessage = field.getAttribute('data-error-message') || 'Please enter a valid value.';
            }
        }
        // Min/Max length validation
        else if (field.hasAttribute('minlength') && value.length < parseInt(field.getAttribute('minlength'))) {
            isValid = false;
            errorMessage = `${this.getFieldLabel(field)} must be at least ${field.getAttribute('minlength')} characters.`;
        }
        else if (field.hasAttribute('maxlength') && value.length > parseInt(field.getAttribute('maxlength'))) {
            isValid = false;
            errorMessage = `${this.getFieldLabel(field)} must be no more than ${field.getAttribute('maxlength')} characters.`;
        }

        // Apply validation state
        this.setFieldValidation(field, isValid, errorMessage);
        return isValid;
    },

    // Get field label for error messages
    getFieldLabel(field) {
        const label = field.form.querySelector(`label[for="${field.id}"]`);
        if (label) {
            return label.textContent.replace('*', '').trim();
        }

        // Fallback to placeholder or name
        return field.placeholder || field.name || 'This field';
    },

    // Clear field validation state
    clearFieldValidation(field) {
        field.classList.remove('is-valid', 'is-invalid');
        field.setAttribute('aria-invalid', 'false');

        const feedback = field.parentNode.querySelector('.invalid-feedback, .valid-feedback');
        if (feedback) {
            feedback.remove();
        }
    },

    // Set field validation state
    setFieldValidation(field, isValid, message = '') {
        if (isValid) {
            field.classList.add('is-valid');
            field.classList.remove('is-invalid');
            field.setAttribute('aria-invalid', 'false');

            if (message) {
                const feedback = document.createElement('div');
                feedback.className = 'valid-feedback';
                feedback.textContent = message;
                field.parentNode.appendChild(feedback);
            }
        } else {
            field.classList.add('is-invalid');
            field.classList.remove('is-valid');
            field.setAttribute('aria-invalid', 'true');

            if (message) {
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = message;
                feedback.setAttribute('role', 'alert');
                field.parentNode.appendChild(feedback);
            }
        }
    },

    // Validate entire form
    validateForm(form) {
        const fields = form.querySelectorAll('.form-control');
        let isFormValid = true;

        fields.forEach(field => {
            const isFieldValid = this.validateField(field);
            if (!isFieldValid) {
                isFormValid = false;
            }
        });

        return isFormValid;
    },

    // Handle form submission
    async handleFormSubmit(form) {
        const isValid = this.validateForm(form);

        if (!isValid) {
            // Focus first invalid field
            const firstInvalidField = form.querySelector('.is-invalid');
            if (firstInvalidField) {
                firstInvalidField.focus();
            }
            return;
        }

        // Get form data before processing
        const formData = this.getFormData(form);
        
        // Preserve form data in case of error
        if (typeof ErrorHandler !== 'undefined') {
            ErrorHandler.preserveUserInput(form, formData);
        }

        // Show loading state
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="loading"></span> Processing...';

        try {
            // Handle different form types with error handling
            const formType = form.dataset.formType || 'generic';
            
            if (typeof ErrorHandler !== 'undefined') {
                // Use error handler for network operations
                await ErrorHandler.handleNetworkErrors(
                    () => this.processForm(formType, formData, form),
                    () => this.handleFormSubmit(form) // Retry callback
                );
            } else {
                await this.processForm(formType, formData, form);
            }
            
            // Clear preserved data on success
            if (typeof ErrorHandler !== 'undefined') {
                const formId = form.id || form.dataset.formType || 'unknown_form';
                ErrorHandler.formDataBackup.delete(formId);
                Utils.storage.remove(`formBackup_${formId}`);
            }
            
        } catch (error) {
            console.error('Form submission error:', error);

            // Use ErrorHandler if available
            if (typeof ErrorHandler !== 'undefined') {
                const errorInfo = {
                    type: this.getErrorType(error),
                    message: error.message,
                    formType: form.dataset.formType,
                    severity: ErrorHandler.SEVERITY.MEDIUM,
                    retryCallback: () => this.handleFormSubmit(form)
                };

                ErrorHandler.handleGlobalError(errorInfo);
                
                // Show recovery options for critical errors
                if (errorInfo.severity === ErrorHandler.SEVERITY.HIGH) {
                    ErrorHandler.showRecoveryOptions(errorInfo);
                }
            } else {
                // Fallback error handling
                const errorMessage = this.getErrorMessage(error);
                MagicMenu.showToast(errorMessage, 'error');
            }
        } finally {
            // Reset button state
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        }
    },

    // Get form data as object
    getFormData(form) {
        const formData = new FormData(form);
        const data = {};

        for (const [key, value] of formData.entries()) {
            data[key] = value;
        }

        return data;
    },

    // Process different types of forms
    async processForm(formType, data, form) {
        switch (formType) {
            case 'contact':
                await this.processContactForm(data, form);
                break;

            case 'checkout':
                await this.processCheckoutForm(data, form);
                break;
            case 'profile-update':
                await this.processProfileUpdateForm(data, form);
                break;
            case 'address':
                await this.processAddressForm(data, form);
                break;
            case 'password-reset':
                await this.processPasswordResetForm(data, form);
                break;
            default:
                console.log('Form submitted:', data);
                MagicMenu.showToast('Form submitted successfully!', 'success');
        }
    },

    // Process contact form
    async processContactForm(data, form) {
        // Simulate API call
        await this.simulateAPICall(1000);

        MagicMenu.showToast('Thank you for your message! We\'ll get back to you soon.', 'success');
        form.reset();
    },



    // Process checkout form
    async processCheckoutForm(data, form) {
        // Simulate API call
        await this.simulateAPICall(3000);

        // Create order data
        const orderData = {
            customerInfo: data,
            items: Cart.items,
            totals: Cart.getTotals(),
            orderTime: new Date().toISOString()
        };

        // Create comprehensive order tracking
        const trackingData = OrderTracking.createOrderWithTracking(orderData);

        // Store as last order for backward compatibility
        Utils.storage.set('lastOrder', {
            orderNumber: trackingData.orderId,
            customerInfo: data,
            items: Cart.items,
            totals: Cart.getTotals(),
            orderTime: trackingData.createdAt,
            estimatedDelivery: trackingData.estimatedDelivery,
            trackingNumber: trackingData.trackingNumber
        });

        // Clear cart
        Cart.clearCart();

        // Request notification permission for order updates
        OrderTracking.requestNotificationPermission();

        MagicMenu.showToast('Order placed successfully! You will receive updates on the progress.', 'success');

        // Redirect to confirmation page
        setTimeout(() => {
            window.location.href = 'confirmation.html';
        }, 1000);
    },

    // Process profile update form
    async processProfileUpdateForm(data, form) {
        // Simulate API call
        await this.simulateAPICall(1500);

        // Get current user data
        const user = Utils.storage.get('user');
        if (user) {
            // Update user data
            const updatedUser = {
                ...user,
                name: `${data.firstName} ${data.lastName}`,
                email: data.email,
                phone: data.phone,
                lastUpdated: new Date().toISOString()
            };

            // Save updated user data
            Utils.storage.set('user', updatedUser, { requiresNetwork: false });

            // Update UI
            document.getElementById('user-name').textContent = updatedUser.name;
            document.getElementById('user-email').textContent = updatedUser.email;
            document.getElementById('profile-name').textContent = updatedUser.name;
            document.getElementById('profile-email').textContent = updatedUser.email;
            document.getElementById('profile-phone').textContent = updatedUser.phone;

            // Hide edit form and show profile view
            document.getElementById('profile-edit').classList.add('hidden');
            document.getElementById('profile-view').classList.remove('hidden');

            MagicMenu.showToast('Profile updated successfully!', 'success');
        }
    },

    // Process address form
    async processAddressForm(data, form) {
        // Simulate API call
        await this.simulateAPICall(1000);

        const addresses = Utils.storage.get('userAddresses', []);
        const addressId = form.getAttribute('data-address-id');

        if (addressId) {
            // Update existing address
            const updatedAddresses = addresses.map(address => {
                if (address.id === addressId) {
                    return {
                        ...address,
                        label: data.label,
                        street: data.street,
                        city: data.city,
                        state: data.state,
                        phone: data.phone || '',
                        instructions: data.instructions || '',
                        isDefault: data.isDefault === 'on' || address.isDefault
                    };
                }
                return data.isDefault === 'on' ? { ...address, isDefault: false } : address;
            });

            Utils.storage.set('userAddresses', updatedAddresses, { requiresNetwork: false });
            MagicMenu.showToast('Address updated successfully!', 'success');
        } else {
            // Add new address
            const newAddress = {
                id: Utils.generateId(),
                label: data.label,
                street: data.street,
                city: data.city,
                state: data.state,
                phone: data.phone || '',
                instructions: data.instructions || '',
                isDefault: data.isDefault === 'on' || addresses.length === 0,
                createdAt: new Date().toISOString()
            };

            // If this is set as default, unset others
            if (newAddress.isDefault) {
                addresses.forEach(address => address.isDefault = false);
            }

            addresses.push(newAddress);
            Utils.storage.set('userAddresses', addresses, { requiresNetwork: false });
            MagicMenu.showToast('Address added successfully!', 'success');
        }

        // Close modal and refresh account page if needed
        const modal = document.getElementById('address-modal');
        if (modal) {
            modal.classList.add('hidden');
        }

        // Refresh account page if currently viewing it
        if (window.location.pathname.includes('account.html')) {
            if (typeof AccountManager !== 'undefined' && AccountManager.checkAuthenticationStatus) {
                AccountManager.checkAuthenticationStatus();
            }
        }
    },

    // Process password reset form
    async processPasswordResetForm(data, form) {
        // Simulate API call
        await this.simulateAPICall(2000);

        // Show success step
        document.getElementById('reset-step-1').classList.add('hidden');
        document.getElementById('reset-step-2').classList.remove('hidden');

        // Store reset request for demo purposes
        Utils.storage.set('passwordResetRequest', {
            email: data.email,
            requestTime: new Date().toISOString(),
            token: Utils.generateId()
        });
    },



    // Toggle password visibility
    togglePasswordVisibility(button) {
        const passwordField = button.parentNode.querySelector('input[type="password"], input[type="text"]');
        if (!passwordField) return;

        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            button.textContent = '🙈';
            button.setAttribute('aria-label', 'Hide password');
        } else {
            passwordField.type = 'password';
            button.textContent = '👁️';
            button.setAttribute('aria-label', 'Show password');
        }
    },

    // Determine error type
    getErrorType(error) {
        if (typeof ErrorHandler === 'undefined') return 'unknown';

        if (error.name === 'NetworkError' || error.message.includes('fetch') || error.message.includes('network')) {
            return ErrorHandler.ERROR_TYPES.NETWORK;
        } else if (error.message.includes('storage') || error.message.includes('quota')) {
            return ErrorHandler.ERROR_TYPES.STORAGE;
        } else if (error.name === 'ValidationError') {
            return ErrorHandler.ERROR_TYPES.VALIDATION;
        } else if (error.message.includes('timeout')) {
            return ErrorHandler.ERROR_TYPES.TIMEOUT;
        }
        
        return ErrorHandler.ERROR_TYPES.UNKNOWN;
    },

    // Get user-friendly error message
    getErrorMessage(error) {
        if (error.name === 'NetworkError' || error.message.includes('fetch')) {
            return 'Network error. Please check your connection and try again.';
        } else if (error.name === 'ValidationError') {
            return 'Please check your input and try again.';
        } else if (error.message.includes('storage')) {
            return 'Unable to save data. Please check your browser settings.';
        } else if (error.message.includes('timeout')) {
            return 'Request timed out. Please try again.';
        }
        
        return 'An error occurred. Please try again.';
    },

    // Simulate API call with delay
    simulateAPICall(delay = 1000) {
        return new Promise(resolve => setTimeout(resolve, delay));
    }
};

// Initialize forms when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    Forms.init();
});
