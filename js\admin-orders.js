/**
 * Magic Menu - Admin Order Management
 * Handles order status updates, filtering, and order operations
 */

const AdminOrderManager = {
    // Initialize order management
    init() {
        this.loadOrderData();
        this.setupEventListeners();
        this.renderOrderTable();
        this.updateDashboardStats();
        console.log('Admin Order Manager initialized');
    },

    // Load order data
    loadOrderData() {
        try {
            // Get orders from the existing order tracking system
            this.orders = this.getAllOrders();
        } catch (error) {
            console.error('Error loading order data:', error);
            this.orders = [];
        }
    },

    // Get all orders from storage
    getAllOrders() {
        const orders = [];
        const keys = Object.keys(localStorage);
        
        keys.forEach(key => {
            if (key.startsWith('orderTracking_')) {
                try {
                    const orderData = JSON.parse(localStorage.getItem(key));
                    if (orderData && orderData.orderId) {
                        orders.push(orderData);
                    }
                } catch (error) {
                    console.error('Error parsing order:', key, error);
                }
            }
        });
        
        // Sort by creation date (newest first)
        return orders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    },

    // Setup event listeners
    setupEventListeners() {
        // Filter controls
        const filterSelect = document.querySelector('#orders-section .filter-controls select');
        if (filterSelect) {
            filterSelect.addEventListener('change', () => this.applyFilters());
        }

        const dateInput = document.querySelector('#orders-section .filter-controls input[type="date"]');
        if (dateInput) {
            dateInput.addEventListener('change', () => this.applyFilters());
        }

        const filterBtn = document.querySelector('#orders-section .filter-controls .btn-primary');
        if (filterBtn) {
            filterBtn.addEventListener('click', () => this.applyFilters());
        }

        // Window resize listener for responsive layout
        window.addEventListener('resize', () => {
            clearTimeout(this.resizeTimeout);
            this.resizeTimeout = setTimeout(() => {
                this.renderOrderTable();
            }, 250);
        });
    },

    // Apply filters
    applyFilters() {
        const statusFilter = document.querySelector('#orders-section .filter-controls select').value;
        const dateFilter = document.querySelector('#orders-section .filter-controls input[type="date"]').value;
        
        let filteredOrders = [...this.orders];
        
        // Filter by status
        if (statusFilter && statusFilter !== 'all') {
            filteredOrders = filteredOrders.filter(order => order.status === statusFilter);
        }
        
        // Filter by date
        if (dateFilter) {
            const filterDate = new Date(dateFilter).toDateString();
            filteredOrders = filteredOrders.filter(order => {
                const orderDate = new Date(order.createdAt).toDateString();
                return orderDate === filterDate;
            });
        }
        
        this.renderOrderTable(filteredOrders);
    },

    // Render order table
    renderOrderTable(ordersToShow = null) {
        const ordersSection = document.querySelector('#orders-section');
        if (!ordersSection) return;

        const orders = ordersToShow || this.orders;

        // Check if we should use mobile layout
        const isMobile = window.innerWidth <= 767;

        if (isMobile) {
            this.renderMobileOrderCards(orders);
        } else {
            this.renderDesktopOrderTable(orders);
        }
    },

    // Render desktop order table
    renderDesktopOrderTable(orders) {
        const tableBody = document.querySelector('#orders-section .data-table tbody');
        if (!tableBody) return;

        if (orders.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center">
                        <p>No orders found.</p>
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = orders.map(order => `
            <tr data-order-id="${order.orderId}">
                <td><strong>${order.orderId}</strong></td>
                <td>
                    ${this.formatDateTime(order.createdAt)}
                </td>
                <td>
                    <strong>${this.escapeHtml(order.customerInfo?.name || 'Unknown Customer')}</strong><br>
                    <small>${this.escapeHtml(order.customerInfo?.phone || 'No phone')}</small>
                </td>
                <td>
                    <div class="order-items">
                        ${this.formatOrderItems(order.items)}
                    </div>
                </td>
                <td><strong>₦${order.totals?.total?.toLocaleString() || '0'}</strong></td>
                <td>
                    <select class="form-control order-status-select"
                            data-order-id="${order.orderId}"
                            onchange="AdminOrderManager.updateOrderStatus('${order.orderId}', this.value)">
                        <option value="placed" ${order.status === 'placed' ? 'selected' : ''}>Placed</option>
                        <option value="confirmed" ${order.status === 'confirmed' ? 'selected' : ''}>Confirmed</option>
                        <option value="preparing" ${order.status === 'preparing' ? 'selected' : ''}>Preparing</option>
                        <option value="ready" ${order.status === 'ready' ? 'selected' : ''}>Ready</option>
                        <option value="out-for-delivery" ${order.status === 'out-for-delivery' ? 'selected' : ''}>Out for Delivery</option>
                        <option value="delivered" ${order.status === 'delivered' ? 'selected' : ''}>Delivered</option>
                        <option value="cancelled" ${order.status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
                    </select>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="AdminOrderManager.viewOrderDetails('${order.orderId}')">View Details</button>
                        ${order.status !== 'delivered' && order.status !== 'cancelled' ?
                            `<button class="btn btn-sm btn-success" onclick="AdminOrderManager.quickStatusUpdate('${order.orderId}')">Quick Update</button>` :
                            ''
                        }
                    </div>
                </td>
            </tr>
        `).join('');
    },

    // Render mobile order cards
    renderMobileOrderCards(orders) {
        const ordersSection = document.querySelector('#orders-section');
        const existingTable = ordersSection.querySelector('.data-table');

        // Hide desktop table
        if (existingTable) {
            existingTable.style.display = 'none';
        }

        // Create or update mobile container
        let mobileContainer = ordersSection.querySelector('.mobile-orders-container');
        if (!mobileContainer) {
            mobileContainer = document.createElement('div');
            mobileContainer.className = 'mobile-orders-container';
            existingTable.parentNode.insertBefore(mobileContainer, existingTable.nextSibling);
        }

        if (orders.length === 0) {
            mobileContainer.innerHTML = `
                <div class="mobile-card">
                    <div class="mobile-card-content text-center">
                        <p>No orders found.</p>
                    </div>
                </div>
            `;
            return;
        }

        mobileContainer.innerHTML = orders.map(order => `
            <div class="mobile-card" data-order-id="${order.orderId}">
                <div class="mobile-card-header">
                    <div>
                        <div class="mobile-card-title">${order.orderId}</div>
                        <div class="mobile-card-subtitle">${this.escapeHtml(order.customerInfo?.name || 'Unknown Customer')}</div>
                    </div>
                    <span class="badge badge-${this.getStatusBadgeClass(order.status)}">${order.status}</span>
                </div>

                <div class="mobile-card-content">
                    <div class="mobile-card-row">
                        <span class="mobile-card-label">Order Time:</span>
                        <span class="mobile-card-value">${new Date(order.createdAt).toLocaleString()}</span>
                    </div>
                    <div class="mobile-card-row">
                        <span class="mobile-card-label">Customer Phone:</span>
                        <span class="mobile-card-value">${this.escapeHtml(order.customerInfo?.phone || 'No phone')}</span>
                    </div>
                    <div class="mobile-card-row">
                        <span class="mobile-card-label">Items:</span>
                        <span class="mobile-card-value">${this.formatOrderItems(order.items)}</span>
                    </div>
                    <div class="mobile-card-row">
                        <span class="mobile-card-label">Total:</span>
                        <span class="mobile-card-value"><strong>₦${order.totals?.total?.toLocaleString() || '0'}</strong></span>
                    </div>
                    <div class="mobile-card-row">
                        <span class="mobile-card-label">Status:</span>
                        <span class="mobile-card-value">
                            <select class="form-control order-status-select"
                                    data-order-id="${order.orderId}"
                                    onchange="AdminOrderManager.updateOrderStatus('${order.orderId}', this.value)">
                                <option value="placed" ${order.status === 'placed' ? 'selected' : ''}>Placed</option>
                                <option value="confirmed" ${order.status === 'confirmed' ? 'selected' : ''}>Confirmed</option>
                                <option value="preparing" ${order.status === 'preparing' ? 'selected' : ''}>Preparing</option>
                                <option value="ready" ${order.status === 'ready' ? 'selected' : ''}>Ready</option>
                                <option value="out-for-delivery" ${order.status === 'out-for-delivery' ? 'selected' : ''}>Out for Delivery</option>
                                <option value="delivered" ${order.status === 'delivered' ? 'selected' : ''}>Delivered</option>
                                <option value="cancelled" ${order.status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
                            </select>
                        </span>
                    </div>
                </div>

                <div class="mobile-card-actions">
                    <button class="btn btn-sm btn-primary" onclick="AdminOrderManager.viewOrderDetails('${order.orderId}')">View Details</button>
                    ${order.status !== 'delivered' && order.status !== 'cancelled' ?
                        `<button class="btn btn-sm btn-success" onclick="AdminOrderManager.quickStatusUpdate('${order.orderId}')">Quick Update</button>` :
                        ''
                    }
                </div>
            </div>
        `).join('');
    },

    // Format date and time
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + '<br><small>' + date.toLocaleTimeString() + '</small>';
    },

    // Format order items
    formatOrderItems(items) {
        if (!items || items.length === 0) return 'No items';
        
        return items.slice(0, 2).map(item => 
            `${this.escapeHtml(item.name)} (${item.quantity})`
        ).join(', ') + (items.length > 2 ? ` +${items.length - 2} more` : '');
    },

    // Escape HTML
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    // Update order status
    async updateOrderStatus(orderId, newStatus) {
        try {
            // Show loading state
            const select = document.querySelector(`select[data-order-id="${orderId}"]`);
            const originalValue = select.value;
            select.disabled = true;
            
            // Simulate API delay
            await this.simulateAPICall(500);
            
            // Get order data
            const orderData = JSON.parse(localStorage.getItem(`orderTracking_${orderId}`));
            if (!orderData) {
                throw new Error('Order not found');
            }
            
            // Update status
            orderData.status = newStatus;
            orderData.updatedAt = new Date().toISOString();
            
            // Add timeline entry
            const statusDescriptions = {
                'placed': 'Order placed',
                'confirmed': 'Order confirmed by restaurant',
                'preparing': 'Order is being prepared',
                'ready': 'Order is ready for pickup/delivery',
                'out-for-delivery': 'Order is out for delivery',
                'delivered': 'Order delivered successfully',
                'cancelled': 'Order cancelled'
            };
            
            orderData.timeline.push({
                status: newStatus,
                timestamp: new Date().toISOString(),
                description: statusDescriptions[newStatus] || 'Status updated',
                location: 'Magic Menu Restaurant',
                automated: false
            });
            
            // Update delivery time if delivered
            if (newStatus === 'delivered') {
                orderData.actualDelivery = new Date().toISOString();
            }
            
            // Save updated order
            localStorage.setItem(`orderTracking_${orderId}`, JSON.stringify(orderData));
            
            // Update local orders array
            const orderIndex = this.orders.findIndex(order => order.orderId === orderId);
            if (orderIndex !== -1) {
                this.orders[orderIndex] = orderData;
            }
            
            // Show success message
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast(`Order ${orderId} status updated to ${newStatus}!`, 'success');
            }
            
            // Update dashboard stats
            this.updateDashboardStats();
            
        } catch (error) {
            console.error('Error updating order status:', error);
            
            // Revert select value
            const select = document.querySelector(`select[data-order-id="${orderId}"]`);
            select.value = originalValue;
            
            if (typeof MagicMenu !== 'undefined' && MagicMenu.showToast) {
                MagicMenu.showToast('Failed to update order status. Please try again.', 'error');
            }
        } finally {
            // Re-enable select
            const select = document.querySelector(`select[data-order-id="${orderId}"]`);
            if (select) {
                select.disabled = false;
            }
        }
    },

    // Quick status update (advance to next logical status)
    quickStatusUpdate(orderId) {
        const order = this.orders.find(order => order.orderId === orderId);
        if (!order) return;
        
        const statusFlow = {
            'placed': 'confirmed',
            'confirmed': 'preparing',
            'preparing': 'ready',
            'ready': 'out-for-delivery',
            'out-for-delivery': 'delivered'
        };
        
        const nextStatus = statusFlow[order.status];
        if (nextStatus) {
            this.updateOrderStatus(orderId, nextStatus);
        }
    },

    // View order details
    viewOrderDetails(orderId) {
        const order = this.orders.find(order => order.orderId === orderId);
        if (!order) return;
        
        const modalHtml = `
            <div class="modal-overlay" id="order-details-modal">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>Order Details - ${order.orderId}</h3>
                            <button type="button" class="modal-close" onclick="AdminOrderManager.closeOrderDetails()">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="order-details-grid">
                                <div class="order-info-section">
                                    <h4>Customer Information</h4>
                                    <div class="info-grid">
                                        <div><strong>Name:</strong> ${this.escapeHtml(order.customerInfo?.name || 'N/A')}</div>
                                        <div><strong>Email:</strong> ${this.escapeHtml(order.customerInfo?.email || 'N/A')}</div>
                                        <div><strong>Phone:</strong> ${this.escapeHtml(order.customerInfo?.phone || 'N/A')}</div>
                                        <div><strong>Address:</strong> ${this.escapeHtml(order.deliveryAddress || 'N/A')}</div>
                                    </div>
                                </div>
                                
                                <div class="order-info-section">
                                    <h4>Order Information</h4>
                                    <div class="info-grid">
                                        <div><strong>Order ID:</strong> ${order.orderId}</div>
                                        <div><strong>Status:</strong> <span class="badge badge-${this.getStatusBadgeClass(order.status)}">${order.status}</span></div>
                                        <div><strong>Order Time:</strong> ${new Date(order.createdAt).toLocaleString()}</div>
                                        <div><strong>Estimated Delivery:</strong> ${order.estimatedDelivery ? new Date(order.estimatedDelivery).toLocaleString() : 'N/A'}</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="order-items-section">
                                <h4>Order Items</h4>
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Item</th>
                                            <th>Quantity</th>
                                            <th>Price</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${order.items.map(item => `
                                            <tr>
                                                <td>${this.escapeHtml(item.name)}</td>
                                                <td>${item.quantity}</td>
                                                <td>₦${item.price.toLocaleString()}</td>
                                                <td>₦${(item.price * item.quantity).toLocaleString()}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="3"><strong>Subtotal:</strong></td>
                                            <td><strong>₦${order.totals?.subtotal?.toLocaleString() || '0'}</strong></td>
                                        </tr>
                                        <tr>
                                            <td colspan="3"><strong>Delivery Fee:</strong></td>
                                            <td><strong>₦${order.totals?.deliveryFee?.toLocaleString() || '0'}</strong></td>
                                        </tr>
                                        <tr>
                                            <td colspan="3"><strong>Total:</strong></td>
                                            <td><strong>₦${order.totals?.total?.toLocaleString() || '0'}</strong></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            
                            <div class="order-timeline-section">
                                <h4>Order Timeline</h4>
                                <div class="timeline">
                                    ${order.timeline.map(event => `
                                        <div class="timeline-item">
                                            <div class="timeline-time">${new Date(event.timestamp).toLocaleString()}</div>
                                            <div class="timeline-content">
                                                <strong>${event.description}</strong>
                                                ${event.location ? `<br><small>Location: ${event.location}</small>` : ''}
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                            
                            ${order.specialInstructions ? `
                                <div class="order-info-section">
                                    <h4>Special Instructions</h4>
                                    <p>${this.escapeHtml(order.specialInstructions)}</p>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="AdminOrderManager.closeOrderDetails()">Close</button>
                            ${order.status !== 'delivered' && order.status !== 'cancelled' ? 
                                `<button type="button" class="btn btn-primary" onclick="AdminOrderManager.quickStatusUpdate('${order.orderId}'); AdminOrderManager.closeOrderDetails();">Update Status</button>` : 
                                ''
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    },

    // Close order details modal
    closeOrderDetails() {
        const modal = document.getElementById('order-details-modal');
        if (modal) {
            modal.remove();
        }
    },

    // Get status badge class
    getStatusBadgeClass(status) {
        const statusClasses = {
            'placed': 'secondary',
            'confirmed': 'info',
            'preparing': 'warning',
            'ready': 'primary',
            'out-for-delivery': 'primary',
            'delivered': 'success',
            'cancelled': 'danger'
        };
        return statusClasses[status] || 'secondary';
    },

    // Update dashboard stats
    updateDashboardStats() {
        const today = new Date().toDateString();
        const todayOrders = this.orders.filter(order => 
            new Date(order.createdAt).toDateString() === today
        );
        
        const pendingOrders = this.orders.filter(order => 
            ['placed', 'confirmed', 'preparing', 'ready', 'out-for-delivery'].includes(order.status)
        );
        
        const todayRevenue = todayOrders.reduce((sum, order) => 
            sum + (order.totals?.total || 0), 0
        );
        
        // Update dashboard stats if on dashboard section
        const dashboardSection = document.getElementById('dashboard-section');
        if (dashboardSection && dashboardSection.classList.contains('active')) {
            this.updateStatCard('Total Orders Today', todayOrders.length);
            this.updateStatCard('Revenue Today', `₦${todayRevenue.toLocaleString()}`);
            this.updateStatCard('Pending Orders', pendingOrders.length);
        }
    },

    // Update stat card
    updateStatCard(label, value) {
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach(card => {
            const labelElement = card.querySelector('.stat-label');
            if (labelElement && labelElement.textContent === label) {
                const numberElement = card.querySelector('.stat-number');
                if (numberElement) {
                    numberElement.textContent = value;
                }
            }
        });
    },

    // Simulate API call
    async simulateAPICall(delay = 1000) {
        return new Promise(resolve => setTimeout(resolve, delay));
    }
};
