/* Magic Menu - Base Styles */

/* CSS Custom Properties (Variables) */
:root {
    /* Colors */
    --primary-color: #ff7a00;        /* Vibrant orange for CTAs and highlights */
    --primary-dark: #e66d00;         /* Darker orange for hover states */
    --primary-light: #ffb366;        /* Lighter orange for backgrounds */
    --secondary-color: #2c3e50;      /* Dark blue for headers and important text */
    --accent-color: #27ae60;         /* Green for success states and fresh elements */
    --text-color: #333333;           /* Standard dark text */
    --text-light: #666666;           /* Lighter text for descriptions */
    --text-muted: #999999;           /* Muted text for less important info */
    --white: #ffffff;
    --background-color: #f8f9fa;     /* Light gray background */
    --card-background: #ffffff;      /* White background for cards */
    --border-color: #dddddd;         /* Light gray borders */
    --light-gray: #cccccc;           /* Slightly darker light gray accents */
    --success-color: #28a745;        /* Green for success messages */
    --error-color: #dc3545;          /* Red for error messages */
    --warning-color: #ffc107;        /* Yellow for warnings */
    --info-color: #17a2b8;           /* Blue for informational messages */

    /* Typography */
    --font-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-body: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;         /* 12px */
    --font-size-sm: 0.875rem;        /* 14px */
    --font-size-base: 1rem;          /* 16px */
    --font-size-lg: 1.125rem;        /* 18px */
    --font-size-xl: 1.25rem;         /* 20px */
    --font-size-2xl: 1.5rem;         /* 24px */
    --font-size-3xl: 2rem;           /* 32px */
    --font-size-4xl: 2.5rem;         /* 40px */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Spacing */
    --spacing-xs: 0.25rem;           /* 4px */
    --spacing-sm: 0.5rem;            /* 8px */
    --spacing-md: 1rem;              /* 16px */
    --spacing-lg: 1.5rem;            /* 24px */
    --spacing-xl: 2rem;              /* 32px */
    --spacing-2xl: 3rem;             /* 48px */
    --spacing-3xl: 4rem;             /* 64px */

    /* Border radius */
    --border-radius-sm: 0.25rem;     /* 4px */
    --border-radius-md: 0.5rem;      /* 8px */
    --border-radius-lg: 0.75rem;     /* 12px */
    --border-radius-xl: 1rem;        /* 16px */

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;

    /* Breakpoints (for reference in media queries) */
    --breakpoint-sm: 320px;          /* Small mobile */
    --breakpoint-md: 768px;          /* Tablet */
    --breakpoint-lg: 1024px;         /* Desktop */
    --breakpoint-xl: 1200px;         /* Large desktop */
}

/* CSS Reset and Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: var(--font-body);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--text-color);
    background-color: var(--background-color);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
}

h1 {
    font-size: var(--font-size-4xl);
}

h2 {
    font-size: var(--font-size-3xl);
}

h3 {
    font-size: var(--font-size-2xl);
}

h4 {
    font-size: var(--font-size-xl);
}

h5 {
    font-size: var(--font-size-lg);
}

h6 {
    font-size: var(--font-size-base);
}

p {
    margin-bottom: var(--spacing-md);
    line-height: var(--line-height-normal);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover,
a:focus {
    color: var(--primary-dark);
    text-decoration: underline;
}

a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Lists */
ul, ol {
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-lg);
}

li {
    margin-bottom: var(--spacing-xs);
}

/* Images */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Forms */
input,
textarea,
select,
button {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for keyboard navigation */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Skip link for accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--secondary-color);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* Container and layout utilities */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.section {
    padding: var(--spacing-2xl) 0;
}

/* Utility classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.hidden {
    display: none;
}

.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}
