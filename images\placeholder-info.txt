Magic Menu - Image Placeholder Information

This directory contains placeholder information for images that would be used in the Magic Menu website.
In a production environment, these would be replaced with high-quality, optimized images.

Required Images:

HERO IMAGES (images/hero/):
- jollof-rice-hero.jpg (1200x800px) - Hero image of jollof rice with grilled chicken
- chef-cooking.jpg (800x600px) - Chef <PERSON><PERSON><PERSON> preparing dishes in kitchen
- og-image.jpg (1200x630px) - Open Graph image for social media sharing

MENU ITEM IMAGES (images/menu/):
- jollof-rice.jpg (400x300px) - Jollof rice with plantain and chicken
- fried-rice.jpg (400x300px) - Nigerian fried rice with vegetables
- coconut-rice.jpg (400x300px) - Coconut rice with grilled fish
- egusi-soup.jpg (400x300px) - Egusi soup with pounded yam
- pepper-soup.jpg (400x300px) - Spicy pepper soup with meat
- oha-soup.jpg (400x300px) - Traditional Oha soup
- bitter-leaf-soup.jpg (400x300px) - Bitter leaf soup
- suya.jpg (400x300px) - Grilled suya skewers
- grilled-fish.jpg (400x300px) - Grilled tilapia
- grilled-chicken.jpg (400x300px) - Grilled chicken
- puff-puff.jpg (400x300px) - Sweet puff puff balls
- chin-chin.jpg (400x300px) - Crunchy chin chin
- meat-pie.jpg (400x300px) - Nigerian meat pie
- pounded-yam-egusi.jpg (400x300px) - Pounded yam with egusi
- amala-ewedu.jpg (400x300px) - Amala with ewedu soup
- fufu-soup.jpg (400x300px) - Fufu with soup
- zobo.jpg (400x300px) - Zobo drink
- chapman.jpg (400x300px) - Chapman cocktail
- palm-wine.jpg (400x300px) - Fresh palm wine
- tiger-nut-drink.jpg (400x300px) - Tiger nut drink

TEAM IMAGES (images/team/):
- chef-adunni.jpg (300x300px) - Professional photo of Chef Adunni
- manager-emeka.jpg (300x300px) - Professional photo of Manager Emeka

ICONS (images/icons/):
- favicon.ico (32x32px) - Website favicon
- apple-touch-icon.png (180x180px) - Apple touch icon
- logo.png (200x60px) - Magic Menu logo

IMAGE OPTIMIZATION GUIDELINES:
- Use WebP format with JPEG fallbacks for better performance
- Implement lazy loading for all images below the fold
- Provide descriptive alt text for accessibility
- Maintain consistent aspect ratios within categories
- Optimize file sizes while maintaining quality
- Use responsive images with multiple sizes for different screen resolutions

PLACEHOLDER REPLACEMENT:
In development, you can use placeholder services like:
- https://picsum.photos/ for general images
- https://foodish-api.herokuapp.com/ for food images
- https://via.placeholder.com/ for specific dimensions

For production, use high-quality, professionally shot images of actual Nigerian dishes and team members.

CONTENT DELIVERY:
Consider using a CDN (Content Delivery Network) for faster image loading:
- Cloudinary
- AWS CloudFront
- Google Cloud CDN
- Azure CDN

ACCESSIBILITY:
- All images must have descriptive alt text
- Decorative images should have empty alt attributes (alt="")
- Consider users with slow internet connections
- Provide text alternatives for important visual information
